{"compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "lib": ["dom", "es2015"], "module": "es2015", "moduleResolution": "node", "sourceMap": true, "target": "es5"}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "src/**/*.spec.ts", "src/**/__tests__/*.ts"], "compileOnSave": false, "atom": {"rewriteTsconfig": false}}