{"name": "flightline", "version": "2.0.0", "author": "Ionic Framework", "homepage": "http://ionicframework.com/", "private": true, "scripts": {"clean": "ionic-app-scripts clean", "build": "ionic-app-scripts build", "lint": "ionic-app-scripts lint", "ionic:build": "ionic-app-scripts build", "ionic:serve": "ionic-app-scripts serve"}, "config": {"ionic_copy": "./scripts/copy-custom-libs.js"}, "dependencies": {"@angular/animations": "^5.2.7", "@angular/cli": "^1.7.4", "@angular/common": "5.2.7", "@angular/compiler": "5.2.7", "@angular/compiler-cli": "5.2.7", "@angular/core": "5.2.11", "@angular/forms": "5.2.11", "@angular/http": "5.2.11", "@angular/platform-browser": "5.2.11", "@angular/platform-browser-dynamic": "5.2.11", "@angular/tsc-wrapped": "^4.4.6", "@ionic-native/badge": "^4.20.0", "@ionic-native/core": "^4.18.0", "@ionic-native/deeplinks": "^4.18.0", "@ionic-native/in-app-browser": "^4.6.0", "@ionic-native/keyboard": "^4.20.0", "@ionic-native/native-storage": "^4.20.0", "@ionic-native/network": "^4.20.0", "@ionic-native/onesignal": "^5.0.0", "@ionic-native/push": "^4.20.0", "@ionic-native/screen-orientation": "4.6.0", "@ionic-native/splash-screen": "4.6.0", "@ionic-native/status-bar": "4.6.0", "@ionic/storage": "2.1.3", "@microsoft/signalr": "3.1.2", "@swimlane/ngx-charts": "7.4.0", "angular2-jwt": "^0.2.3", "compare-func": "^2.0.0", "cordova": "^8.1.2", "cordova-plugin-device": "^2.0.3", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-nativestorage": "2.3.2", "cordova-plugin-screen-orientation": "^3.0.2", "cordova-plugin-splashscreen": "^5.0.3", "cordova-plugin-statusbar": "^2.4.3", "cordova-plugin-whitelist": "^1.3.4", "cordova-support-google-services": "^1.3.2", "deep-diff": "^1.0.2", "es6-promise-plugin": "^4.2.2", "font-awesome": "^4.7.0", "ionic-angular": "3.9.2", "ionic-plugin-deeplinks": "^1.0.18", "ionicons": "3.0.0", "ios": "0.0.1", "moment": "^2.22.2", "ngx-cookie-service": "^1.0.10", "phonegap-plugin-multidex": "^1.0.0", "rxjs": "5.5.11", "sw-toolbox": "3.6.0", "typescript": "^2.9.2", "typescript-string-operations": "^1.4.1", "zone.js": "0.8.18", "cordova-ios": "0.0.1"}, "devDependencies": {"@ionic/app-scripts": "^3.1.8", "@types/node": "^10.5.2", "cordova-android": "^11.0.0", "cordova-browser": "^5.0.4", "cordova-ios": "^6.3.0", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-badge": "^0.8.8", "cordova-plugin-inappbrowser": "^5.0.0", "cordova-plugin-ionic-webview": "^5.0.0", "onesignal-cordova-plugin": "^3.3.1", "typescript": "2.4.2"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-device": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-support-google-services": {}, "cordova-plugin-statusbar": {}, "cordova-plugin-screen-orientation": {}, "cordova-plugin-nativestorage": {}, "ionic-plugin-deeplinks": {"URL_SCHEME": "flightline", "DEEPLINK_SCHEME": "https", "DEEPLINK_HOST": "flightline-control.com", "ANDROID_PATH_PREFIX": "/", "ANDROID_2_PATH_PREFIX": "/", "ANDROID_3_PATH_PREFIX": "/", "ANDROID_4_PATH_PREFIX": "/", "ANDROID_5_PATH_PREFIX": "/", "DEEPLINK_2_SCHEME": " ", "DEEPLINK_2_HOST": " ", "DEEPLINK_3_SCHEME": " ", "DEEPLINK_3_HOST": " ", "DEEPLINK_4_SCHEME": " ", "DEEPLINK_4_HOST": " ", "DEEPLINK_5_SCHEME": " ", "DEEPLINK_5_HOST": " "}, "cordova-plugin-androidx": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-badge": {}, "cordova-plugin-inappbrowser": {}, "onesignal-cordova-plugin": {}}, "platforms": ["android", "browser", "ios"]}}