@font-face {
  font-family: 'fontello';
  src: url('../font/fontello.eot?31976995');
  src: url('../font/fontello.eot?31976995#iefix') format('embedded-opentype'),
       url('../font/fontello.woff2?31976995') format('woff2'),
       url('../font/fontello.woff?31976995') format('woff'),
       url('../font/fontello.ttf?31976995') format('truetype'),
       url('../font/fontello.svg?31976995#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?31976995#fontello') format('svg');
  }
}
*/
 
 [class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: none;
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
 
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.icon-temperature_sensor:before { content: '\e800'; } /* '' */
.icon-windspeedicon:before { content: '\e801'; } /* '' */
.icon-water_level:before { content: '\e802'; } /* '' */
.icon-co:before { content: '\e803'; } /* '' */
.icon-co2:before { content: '\e804'; } /* '' */
.icon-ammonia:before { content: '\e805'; } /* '' */
.icon-current:before { content: '\e806'; } /* '' */
.icon-pressure_gauge:before { content: '\e807'; } /* '' */
.icon-humidity:before { content: '\e808'; } /* '' */
.icon-watermeter:before { content: '\e809'; } /* '' */