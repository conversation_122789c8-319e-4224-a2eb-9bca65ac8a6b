
// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/v2/theming/

// Font path is used to include ionicons,
// roboto, and noto sans fonts
$font-path: "../assets/fonts";


// The app direction is used to include
// rtl styles in your app. For more info, please see:
// http://ionicframework.com/docs/theming/rtl-support/
$app-direction: ltr;


@import "ionic.globals";


// Shared Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass variables found in Ionic's source scss files.
// To view all the possible Ionic variables, see:
// http://ionicframework.com/docs/v2/theming/overriding-ionic-variables/




// Named Color Variables
// --------------------------------------------------
// Named colors makes it easy to reuse colors on various components.
// It's highly recommended to change the default colors
// to match your app's branding. Ionic uses a Sass map of
// colors so you can add, rename and remove colors as needed.
// The "primary" color is the only required color in the map.


$colors: (
    primary:   (base: #03A9F4, contrast:#000),
    secondary:  (base: #A1E617, contrast: #000),
    danger:     #f43e00,
    light:      (base:#bdc3c7, contrast:#000),
    dark:       (base:#d35400, contrast:#000),
    outline:    (base: #474747, contrast:rgb(221, 221, 221)),
    notallowed: (base: #252525, contrast: rgb(221, 221, 221)),
    alert:      #F39C12,
    twitter:    #1da1f4,
    google:     #dc4a38,
    vimeo:      #23b6ea,
    facebook:   #3b5998,
    favorite:     #FFD700,
    online:       #20d81a,
    offline:      #e5210b,
    warning:      #fff200,
    inactive:     #808287,
    header:       #ededed,
    acknowledged: #d0d302,
    alarm0:       #e5210b,
    alarm1:       #d0d302,
    alarm2:       #A1E617,
    alarm3:       #0095FE
  );


// App iOS Variables
// --------------------------------------------------
// iOS only Sass variables can go here




// App Material Design Variables
// --------------------------------------------------
// Material Design only Sass variables can go here




// App Windows Variables
// --------------------------------------------------
// Windows only Sass variables can go here




// App Theme
// --------------------------------------------------
// Ionic apps can have different themes applied, which can
// then be future customized. This import comes last
// so that the above variables are used and Ionic's
// default are overridden.

//@import "ionic.theme.default";
@import "./dark/dark.scss";


// Ionicons
// --------------------------------------------------
// The premium icon font for Ionic. For more info, please see:
// http://ionicframework.com/docs/v2/ionicons/

@import "ionic.ionicons";


// Fonts
// --------------------------------------------------

@import "roboto";
@import "noto-sans";
