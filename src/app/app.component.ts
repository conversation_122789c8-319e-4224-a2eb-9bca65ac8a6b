import { Component, ViewChild } from '@angular/core';
import { Platform, Nav, MenuController, App, AlertController, Events, ToastController } from 'ionic-angular';
import { StatusBar } from '@ionic-native/status-bar';
import { SplashScreen } from '@ionic-native/splash-screen';
import { PageInterface } from '../models/page-interface.model';
import { AuthService } from '../services/auth.service';
import { SignalRService } from '../services/signalr.service';
import { PushOptions, PushObject, RegistrationEventResponse, NotificationEventResponse, Push } from '@ionic-native/push';
import { LocalStorageHelper } from '../providers/storage-helper.provider';
import { PushRegistrationProvider } from '../providers/push-registration.provider';
import { OrganizationDataProvider } from '../providers/org-data.provider';
import { DBKeys } from '../models/dbkeys.static';
import { PushMessageHandler } from '../services/pushmessagehandler.service';
import { Observable, Subscription } from 'rxjs';
import { Site } from '../models/site.model';
import { SiteDataProvider } from '../providers/site-data.provider';
import { SiteContextService } from '../services/site-context.service';
import { AlarmDetailsWrapper } from '../pages/alarm-details/alarm-details-wrapper.component';
import { ControlDetailsWrapper } from '../pages/control-details/control-details-wrapper.component';
import { OfflineAlertDetailsPage } from '../pages/offline-alert-details/offline-alert-details';
import { Keyboard } from '@ionic-native/keyboard';
import { ScreenOrientation } from '@ionic-native/screen-orientation';
import { AlarmDetailsTabs } from '../pages/alarm-details/alarm-details-tabs/alarm-details-tabs.component';
import { ControlDataProvider } from '../providers/control-data.provider';
import { AlarmDataProvider } from '../providers/alarm-data.provider';
import { Organization } from '../models/organization.model';
import { NativeStorage } from '@ionic-native/native-storage';
import { ControlDetailsTabs } from '../pages/control-details/control-details-tabs/control-details-tabs.component';
import OneSignal from 'onesignal-cordova-plugin';
import { ConfigurationService } from '../services/configuration.service';
import { AlarmActions } from '../pages/alarm-details/alarm-actions/alarm-actions.component';
import { ControlAlarm } from '../models/control-alarm-model';
import { MessageType } from '../models/types/message-type';
import { AlarmState } from '../models/types/alarm-state';
import { OfflineAlert } from '../models/offline-alert.model';
import * as _ from 'lodash';
import { Deeplinks } from '@ionic-native/deeplinks';
import { AddControlPage } from '../pages/add-control/add-control';
import { User } from '../models/user-model';
import { UserDataProvider } from '../providers/user-data.provider';
import { RegisterUserPage } from '../pages/register-user/register-user';

@Component({
  templateUrl: 'app.template.html'
})
export class FlightLineApp {

  @ViewChild(Nav, null) nav: Nav;
  private alert;

  protected appPages: PageInterface[] = [
    { title: 'Dashboard', name: 'home-page', index: 1, icon: 'home' },
    { title: 'Controls', name: 'controls-list', index: 2, icon: 'desktop' },
    { title: 'Alarms', name: 'alarms-list', index: 3, icon: 'notifications' },
    { title: 'Entities', name: 'rooms-page', index: 4, icon: 'speedometer' },
    { title: 'Offline Alerts', name: 'offline-alerts', index: 5, icon: 'alert' }
    // { title: 'Map', name: 'TabsPage', component: TabsPage, tabComponent: MapPage, index: 2, icon: 'map' },
    // { title: 'About', name: 'TabsPage', component: TabsPage, tabComponent: AboutPage, index: 3, icon: 'information-circle' }
  ];

  protected loggedInPages: PageInterface[] = [
    { title: 'Settings', name: 'user-settings-page', index: 3, icon: 'cog' },
    { title: 'Logout', name: 'home-page', icon: 'log-out', logsOut: true }
  ];

  protected loggedOutPages: PageInterface[] = [
    { title: 'Login', name: 'login-page', icon: 'log-in', index: 0 },
    // { title: 'Support', name: 'SupportPage', icon: 'help' },
    // { title: 'Signup', name: 'SignupPage', icon: 'person-add' }
  ];

  protected pushOptions: PushOptions = {
    android: {},
    ios: {
      alert: 'true',
      badge: true,
      sound: 'true'
    },
    browser: {
      pushServiceURL: 'http://push.api.phonegap.com/v1/push'
    }
  }

  public rootPage: any;
  public currentUser: string;
  public sites: Observable<Site[]>;
  private organizations: Observable<Organization[]>;
  public orgName: string;
  public orgId: string;
  private pushInterval: Subscription;
  public pushEnabled = false;


  constructor(private platform: Platform,
    private statusBar: StatusBar,
    private splashScreen: SplashScreen,
    public menu: MenuController,
    private authService: AuthService,
    private events: Events,
    private push: Push,
    private signalRService: SignalRService,
    private storageHelper: LocalStorageHelper,
    private pushRegistration: PushRegistrationProvider,
    private orgData: OrganizationDataProvider,
    private pushMessageHandler: PushMessageHandler,
    private app: App,
    private siteData: SiteDataProvider,
    private siteContext: SiteContextService,
    private alertCtrl: AlertController,
    private keyboard: Keyboard,
    private screen: ScreenOrientation,
    private controlData: ControlDataProvider,
    public toastController: ToastController,
    private alarmData: AlarmDataProvider,
    private nativeStorage: NativeStorage,
    private menuCtrl: MenuController,
    private deeplinks: Deeplinks,
    private userData: UserDataProvider) {

    platform.ready().then(() => {

      // Okay, so the platform is ready and our plugins are available.
      // Here you can do any higher level native things you might need.
      this.statusBar.styleDefault();
      this.statusBar.show();
      splashScreen.hide();
      var bigToken = localStorage.getItem('auth-tokens');
      if (bigToken) {
        var parsedToken = JSON.parse(bigToken);
        console.log("Decoded Token", token);
        this.authService.setAuthToken(parsedToken.access_token)
        localStorage.setItem('refresh_token', parsedToken.refresh_token);
      }
      var token = localStorage.getItem('auth_token');
      if (!token) {
        this.enableMenu(false);
        this.nav.setRoot('login-page');
      }
      else {
        this.enableMenu(true);
        this.nav.setRoot('home-page');
      }
      // this.checkForUpdate();
      var startup = true;
      if(localStorage.getItem('username')) this.currentUser = localStorage.getItem('username').trim();
      this.orgName = localStorage.getItem(DBKeys.SELECTED_ORG_NAME);
      this.orgId = localStorage.getItem(DBKeys.SELECTED_ORG_ID);
      this.startupAuth();
      this.sites = this.siteData.getSitesBinding();
      this.siteData.getSites();
      signalRService.connect();
      this.events.subscribe("OrgChange", () => {
        this.orgName = localStorage.getItem(DBKeys.SELECTED_ORG_NAME);
        this.orgId = localStorage.getItem(DBKeys.SELECTED_ORG_ID);
      });
      this.events.subscribe('logout', () => {
        this.nav.setRoot('login-page');
      });
      this.authService.loginStatus$.subscribe((status) => {
        if (!status && !startup) {
          console.log('User is not logged in... ');
          this.enableMenu(false);
          this.nav.setRoot('login-page');
          signalRService.disconnect();
        }
        else if (status) {
          this.currentUser = localStorage.getItem('username').trim();
          console.debug('User is logged in...');
          this.events.publish('login');
          this.authService.startRefreshTimer();
          this.enableMenu(true);
          this.getOrgs();
          this.userData.getUserInfo().subscribe((data: User) => {
            localStorage.setItem("user_info", JSON.stringify(data));
          })

          if(!startup) signalRService.connect();
          startup = false;
        }
        else {
          startup = false;
        }
      });

      this.events.subscribe("AlarmNav", (alarm => {
        this.handleAlarmPushNav(alarm);
      }));

      this.events.subscribe("AlertNav", (offlineAlert => {
        this.handleAlertPushNav(offlineAlert);
      }));

      this.events.subscribe("PopToRoot", () => {
        this.nav.pop();
      })
      // this.initDeeplinks();
      this.initPush();
      this.hookPlatformEvents();
      document.onkeypress = (e => {
        if (e.keyCode == 13) this.keyboard.hide();
      });
      if (this.platform.is('cordova')) this.screen.lock('portrait');
      //Handling for deeplinks
     

      //#region Alternate deep link logic - DO NOT DELETE
      // this.deeplinks.routeWithNavController(this.nav, {
      //   "/add-control": {}
      // }).subscribe((match) => {
      //   console.info("DeeplinksNav Reached here", match);
      //   alert(JSON.stringify(match));
      //   //this.handleAddControlRedirect();
      // }, (nomatch) => {
      //   console.info("DeeplinksNav Reached here no match", nomatch);
      //   alert(JSON.stringify(nomatch));
      // });
      //#endregion

    });

    this.platform.registerBackButtonAction(() => {
      const modal = this.app._appRoot._modalPortal.getActive();
      const actionSheet = this.app._appRoot._overlayPortal.getActive()
      const nav = this.app.getActiveNav();

      if (actionSheet && actionSheet.dismiss()) {
        actionSheet.dismiss();
      }
      else if (modal && modal.dismiss()) {
        modal.dismiss();
      }
      else if (this.menu.isOpen()) {
        this.menu.close();
      }
      else if (nav.canGoBack()) {
        nav.pop();
      }
      else if (this.nav.canGoBack()) {
        this.nav.pop();
      }
      else {
        if (this.nav.getActive().id == 'login-page') return;
        this.nav.setRoot('home-page')
      }

    }, 2);
  }

  initDeeplinks(){
    if(!this.platform.is('cordova')) return;
    var sub = this.deeplinks.route(
      {"/add-control": 'AddControlPage'}).subscribe((match) => {
      console.log("Deeplinks Reached here", match);
      //Check if control exists before proceeding further
      this.handleAddControlDeeplink(match.$args.serialNo);
     
    }, (nomatch) => {
      console.log("Deeplinks Reached here no match", nomatch);
      var path = nomatch.$link.path;
      var split = path.split('/')
      console.log("Deeplinks Reached here no match path", split);
      if(split.length == 3){
        if(split[1] == 'add-control'){
          var serial = split[2];
          this.handleAddControlDeeplink(serial);
        }
        
      }
      sub.unsubscribe();
      this.initDeeplinks();
    });
  }

  handleAddControlDeeplink(serial: string){
    this.controlData.checkSerialNumberExists(serial).subscribe((data) => {
      //If control exists then show an error message
      console.log("Check serial result", data);
      if (data == true) {
        console.log("Reached inside data true");
        var statusToast = this.toastController.create({
          message: "Add control failed. Scanned control (" + serial + ") already present in the system." ,
          duration: 10000,
          position: 'top',
          showCloseButton: true,
          dismissOnPageChange: true,
          closeButtonText: 'Close'
        })
    
        statusToast.onDidDismiss(() => {
          statusToast = null;
        });
    
        statusToast.present();
      }
      //If control doesn't exist then proceed with the add process
      else {
        console.log("Reached inside data false");
        //If user is already logged in then push them to add control page
        if (localStorage.getItem("auth_token") != null || localStorage.getItem("auth_token") != undefined) {
          this.nav.push(AddControlPage, { serialNo: serial, isNewUser: false })
        }
        //If user is not logged in then push them to register user page
        else {
          //navigate user to register user page
          this.nav.push(RegisterUserPage);
          //save the scanned serial number to storage
          localStorage.setItem("scanned_serial_no", serial)
        }
      }
    })        
  }

  goToNewControl(){
    this.nav.push(AddControlPage, { serialNo: 1112222333, isNewUser: false })
  }

  handleAddControlRedirect() {
    this.nav.push('page-add-control', {})
  }


  handleAlarmPushNav(alarm) {
    if (this.nav.getActive() == null) window.setTimeout(() => this.handleAlarmPushNav(alarm), 1000)
    else if (this.nav.getActive().component == null) this.nav.setRoot('alarm-actions-page', alarm)
    else if (this.nav.getActive().component == AlarmActions) {
      this.events.publish('AlarmSwitch', alarm)
    }
    else {
      this.nav.push('alarm-actions-page', { alarm: alarm })
    }
  }

  handleAlertPushNav(offlineAlert) {
    if (this.nav.getActive() == null) window.setTimeout(() => this.handleAlertPushNav(offlineAlert), 1000)
    else if (this.nav.getActive().component == null) this.nav.setRoot('offline-alert-details', { offlineAlert: offlineAlert })
    else if (this.nav.getActive().component == OfflineAlertDetailsPage) this.events.publish('AlertSwitch', offlineAlert)
    else {
      this.nav.push('offline-alert-details', { offlineAlert: offlineAlert })
    }
  }

  startupAuth() {
    this.authService.refreshToken()
      .catch(() => {
        window.setTimeout(() => this.startupAuth(), 5000);
        return null;
      })
      .subscribe();
  }

  getOrgs() {
    this.organizations = this.orgData.getOrganizationsBinding();
    this.organizations.subscribe((orgs: any[]) => {
      if (orgs && orgs.length > 0) {

        if (!localStorage.getItem(DBKeys.SELECTED_ORG_ID)) {
          localStorage.setItem(DBKeys.SELECTED_ORG_ID, orgs[0].organizationId.toString());
          localStorage.setItem(DBKeys.SELECTED_ORG_NAME, orgs[0].name);
        }
        this.orgName = localStorage.getItem(DBKeys.SELECTED_ORG_NAME);
        this.siteData.getSitesMap();
      }
    });
    this.orgData.getOrganizations();
  }

  changeOrg() {
    this.nav.setRoot('user-settings-page');
    this.menu.close();
  }

  logout() {
    this.alertCtrl.create({
      title: 'Logout',
      message: 'Are you sure you wish to logout',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Logout',
          handler: () => {
            this.nav.setRoot('login-page');
            this.authService.logout();
            this.currentUser = "";
            localStorage.removeItem("username")
            this.events.publish('logout');
            this.siteContext.clearSitesMap();
            this.orgName = null;
            this.orgId = null;
          }
        }
      ]
    }).present();
  }


  private hookPlatformEvents() {
    this.platform.resume.subscribe(() => {
      this.authService.startRefreshTimer();
      this.alarmData.getMinimalAlarms();
      this.pushMessageHandler.startPushQueue();
      this.controlData.getControls().subscribe();
      this.alarmData.getAlarms().subscribe();
    });

    this.platform.pause.subscribe(() => {
      this.alarmData.rehandleAlarmCountBadge();
      this.pushMessageHandler.pausePushQueue();
      this.authService.stopRefreshTimer();
      this.alarmData.rehandleAlarmCountBadge();
    })
  }

  enableMenu(loggedIn: boolean) {
    this.menu.enable(loggedIn, 'loggedInMenu');
    this.menu.enable(!loggedIn, 'loggedOutMenu');
    this.menu.enable(loggedIn, 'sitesList');
  }

  navToSiteAlarms(site: Site) {
    this.nav.setRoot('site-alarms-list', { site: site })
    this.menu.close();
  }

  public isAllSelected(sites) {
    return this.siteContext.selectedSiteIds.length == sites.length;
  }

  public toggleSite(site: Site) {
    if (this.isSiteSelected(site)) this.siteContext.removeSiteId(site.siteId);
    else this.siteContext.addSiteId(site.siteId);
  }

  public isSiteSelected(site: Site) {
    return this.siteContext.isSiteSelected(site.siteId);
  }

  openPage(page: PageInterface) {
    let params = {};

    // the nav component was found using @ViewChild(Nav)
    // setRoot on the nav to remove previous pages and only have this page
    // we wouldn't want the back button to show in this scenario
    // if (page.index) {
    //   params = { tabIndex: page.index };
    // }

    // If we are already on tabs just change the selected tab
    // don't setRoot again, this maintains the history stack of the
    // tabs even if changing them from the menu
    // if (this.nav.getActiveChildNavs().length && page.index != undefined) {
    //   this.nav.getActiveChildNavs()[0].select(page.index);
    // } else {
    // Set the root of the nav with params if it's a tab index
    this.nav.setRoot(page.name);
    // console.log(`Didn't set nav root: ${err}`);
    //   });
    // }

    if (page.logsOut === true) {
      // Give the menu time to close before changing to logged out
      this.authService.logout();
    }
  }

  isActive(page: PageInterface) {
    let childNav = this.nav.getActiveChildNavs()[0];

    // Main nav active
    if (this.nav.getActive() && this.nav.getActive().id === page.name) {
      return 'primary';
    }

    //Tabs are a special case because they have their own navigation
    if (childNav) {
      if (childNav.getSelected() && childNav.getSelected().root === page.tabComponent) {
        return 'primary';
      }
      return;
    }

    if (this.nav.getActive() && this.nav.getActive().name === page.name) {
      return 'primary';
    }
    return;
  }


  private initPush() {

    //   this.push.register().then((token: PushToken) => {
    //     this.push.saveToken(token, { ignore_user: true }).then((savedToken: PushToken) => {
    //       this.storage.set('DeviceId', savedToken);
    //     });
    //   });

    //   this.push.rx.notification().subscribe((pushMessage: PushMessage) => {
    //     console.log("Push received", pushMessage)
    //     this.pushMessageHandler.processPushMessage(pushMessage, this.nav, this.app);
    //   });
    console.log("Is this Cordova?", this.platform.is('cordova'));
    if (!this.platform.is('cordova')) return;
    this.platform.ready().then(() => {
      console.log("Initting push")
      OneSignal.setLogLevel(6,0)
      // this.oneSignal.startInit(ConfigurationService.oneSignalAppId, '859468141054');
      OneSignal.setAppId(ConfigurationService.oneSignalAppId);
      // this.oneSignal.inFocusDisplaying(this.oneSignal.OSInFocusDisplayOption.None);
      // OneSignal.deleteTag("username");
      if (this.currentUser) OneSignal.sendTag("username", this.currentUser.trim())
      else{
        console.log("User not found, resetting sub")
        OneSignal.deleteTag("username");
        // OneSignal.disablePush(true)
      }
      OneSignal.addPermissionObserver(res => {
        console.log("Permission response", res)
      })
      OneSignal.getDeviceState(res => {
        console.log("Device state", res)
        this.pushEnabled = res.hasNotificationPermission;
      })

      OneSignal.setNotificationOpenedHandler((data) => {
        console.log("Notification Received", data)
          OneSignal.removeNotification(data.notification.androidNotificationId)
          this.pushMessageHandler.processPushMessage(data.notification, true)
          this.alarmData.rehandleAlarmCountBadge()
      });
      OneSignal.setNotificationWillShowInForegroundHandler((data) => {
        console.log("Foreground Notification Received", data)
        OneSignal.removeNotification(data.getNotification().androidNotificationId)
        this.pushMessageHandler.processPushMessage(data.getNotification())
            this.alarmData.rehandleAlarmCountBadge()
      })
      // this.oneSignal.handleNotificationOpened().subscribe((data: any) => {
      //   // console.log("Notification Opened", data)
      //   if (!data.notification.isAppInFocus) {
      //     this.pushMessageHandler.processPushMessage(data.notification)
      //     this.alarmData.rehandleAlarmCountBadge()
      //   }
      // });
      this.events.subscribe('login', () => {
        console.log("Login event detected, subbing")
          if(this.pushInterval) return;
          // OneSignal.deleteTag('username')
          // this.pushInterval = Observable.interval(5000).subscribe(() => {
          //   console.log("push interval running")
          //   OneSignal.getTags((data) => {
          //     console.log(data)
          //     // OneSignal.disablePush(false);
          //     OneSignal.setAppId(ConfigurationService.oneSignalAppId);
              if(this.currentUser) OneSignal.sendTag("username", this.currentUser.trim())
            })
             
          // })

      // });
      this.events.subscribe('logout', () => {
        OneSignal.deleteTag("username");
      });
    });
  }

  private goToPushSettings(){
    OneSignal.promptForPushNotificationsWithUserResponse(true);
  }
}