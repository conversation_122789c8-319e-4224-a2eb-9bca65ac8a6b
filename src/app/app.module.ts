import { BrowserModule } from '@angular/platform-browser';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { IonicApp, IonicErrorHandler, IonicModule, IonicPageModule, NavController } from 'ionic-angular';
import { SplashScreen } from '@ionic-native/splash-screen';
import { StatusBar } from '@ionic-native/status-bar';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { FlightLineApp } from './app.component';
import { AuthService } from '../services/auth.service';
import { ConfigurationService } from '../services/configuration.service';
import { ControlDataProvider } from '../providers/control-data.provider';
import { CookieService } from 'ngx-cookie-service';
import { TokenInterceptor } from '../providers/token.interceptor';
import { IonicStorageModule } from '@ionic/storage';
import { LoginPage } from '../pages/login/login-page';
import { OrgContextService } from '../services/org-context.service';
import { SearchService } from '../services/search.service';
import { AlarmDataProvider } from '../providers/alarm-data.provider';
import { AppErrorHandler } from '../providers/error-handler';
import { RoomDataProvider } from '../providers/room-data.provider';
import { AlarmStateActivePipe, AlarmStateOtherPipe } from '../pipes/alarm-status.pipe';
import { PipesModule } from '../pipes/pipes.module';
import { HubConnectionFactory } from '../signalr-client';
import { SignalRService } from '../services/signalr.service';
import { OfflineAlertsList } from '../components/offline-alerts/offline-alerts-list.component';
import { OfflineAlertDataProvider } from '../providers/offine-alert-data.provider';
import { LiveValuesSubscription } from '../providers/livevalues-subscription.provider';
import { OrganizationDataProvider } from '../providers/org-data.provider';
import { UserDataProvider } from '../providers/user-data.provider';
import { LocalStorageHelper } from '../providers/storage-helper.provider';
import { Push, PushObject, PushOptions } from '@ionic-native/push';
import { LiveValueService } from '../services/live-value.service';
import { PushRegistrationProvider } from '../providers/push-registration.provider';
import { GraphService } from '../services/graph.service';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ExtendedLiveDataProvider } from '../providers/extended-livedata.provider';
import { RemoteControlService } from '../services/remote-control-service';
import { RemoteSettingsService } from '../services/remote-settings-service';
import { EntitiesDataProvider } from '../providers/entities-data.provider';
import { RemoteControlProvider } from '../providers/remote-control.provider';
import { PushMessageHandler } from '../services/pushmessagehandler.service';
import { HttpService } from '../services/http-service';
import { Network } from '@ionic-native/network';
import { SiteDataProvider } from '../providers/site-data.provider';
import { SiteContextService } from '../services/site-context.service';
import { AlertDataProvider } from '../providers/alert-data-provider';
import { Keyboard } from '@ionic-native/keyboard';
import { ScreenOrientation } from '@ionic-native/screen-orientation';
import { InAppBrowser } from '@ionic-native/in-app-browser';
import { AlarmCountComponent } from '../components/alarm-count/alarm-count.component';
import { AlarmCountComponentModule } from '../components/alarm-count/alarm-count.component.module';
import { RemoteSettingsProvider } from '../providers/remote-settings-provider';
import { NativeStorage } from '@ionic-native/native-storage';
import { OneSignal } from '@ionic-native/onesignal';
import { Badge } from '@ionic-native/badge';
import { IgnoredControlsPageModule } from '../pages/ignored-controls/ignored-controls.component.module';
import { Deeplinks } from '@ionic-native/deeplinks';
import { AddControlPageModule } from '../pages/add-control/add-control.module';
import { RegisterUserPageModule } from '../pages/register-user/register-user.module';


@NgModule({
  declarations: [
    FlightLineApp,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    IonicStorageModule,
    PipesModule,
    IgnoredControlsPageModule,
    AlarmCountComponentModule,
    AddControlPageModule,
    RegisterUserPageModule,
    IonicModule.forRoot(FlightLineApp, {})
  ],
  bootstrap: [IonicApp],
  entryComponents: [
    FlightLineApp,
  ],
  providers: [
    StatusBar,
    Keyboard,
    ScreenOrientation,
    InAppBrowser,
    CookieService,
    SplashScreen,
    AuthService,
    ControlDataProvider,
    AlarmDataProvider,
    RoomDataProvider,
    SignalRService,
    ConfigurationService,
    OrganizationDataProvider,
    OrgContextService,
    HubConnectionFactory,
    SearchService,
    AppErrorHandler,
    OfflineAlertDataProvider,
    LocalStorageHelper,
    LiveValuesSubscription,
    LiveValueService,
    PushRegistrationProvider,
    ExtendedLiveDataProvider,
    EntitiesDataProvider,
    PushMessageHandler,
    HttpService,
    AlertDataProvider,
    Push,
    Network,
    GraphService,
    RemoteControlProvider,
    RemoteControlService,
    RemoteSettingsService,
    SiteDataProvider,
    UserDataProvider,
    SiteContextService,
    NativeStorage,
    Badge,
    { provide: ErrorHandler, useClass: IonicErrorHandler },
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
    RemoteSettingsProvider,
    Deeplinks
  ]
})
export class AppModule {
  constructor(factory: HubConnectionFactory) {
    factory.create(
      { key: 'AppDataHub', endpointUri: 'http://localhost:5000/signalr/applicationhub' }
    );
  }
}
