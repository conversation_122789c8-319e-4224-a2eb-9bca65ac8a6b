<!-- <ion-split-pane> -->
<!-- logged out menu -->
<ion-menu id="loggedOutMenu" side="left" [content]="content">
    <ion-header>
        <ion-toolbar>
            <ion-title>Menu</ion-title>
        </ion-toolbar>
    </ion-header>

    <ion-content class="outer-content">
        <!-- <ion-list>
                <ion-list-header>
                    Navigate
                </ion-list-header>
                <button ion-item menuClose *ngFor="let p of appPages" (click)="openPage(p)">
                    <ion-icon item-start [name]="p.icon" [color]="isActive(p)"></ion-icon>
                    {{p.title}}
                </button>
            </ion-list> -->
        <ion-list>
            <ion-list-header>
                Account
                <br />
            Version: 3.1.31
            </ion-list-header>
            <button ion-item menuClose *ngFor="let p of loggedOutPages" (click)="openPage(p)">
                <ion-icon item-start [name]="p.icon" [color]="isActive(p)"></ion-icon>
                {{p.title}}
            </button>
        </ion-list>
        <!-- <ion-list>
                <ion-list-header>
                    Tutorial
                </ion-list-header>
                <button ion-item menuClose (click)="openTutorial()">
                    <ion-icon item-start name="hammer"></ion-icon>
                    Show Tutorial
                </button>
            </ion-list> -->
    </ion-content>
</ion-menu>

<!-- logged in menu -->
<ion-menu id="loggedInMenu" side="left" [content]="content">
    <ion-header>
        <ion-toolbar>
            <ion-title>Menu</ion-title>
        </ion-toolbar>

    </ion-header>
    <ion-content class="outer-content">
        <ion-list style="margin-bottom: 0px">
            <ion-list-header style="zoom:.8" color="primary">
                {{currentUser}}
                <ion-icon (click)="logout()" item-right
                    style="color: red; margin-right: 15px; margin-bottom: 2px; font-size: 2.5em; font-weight: 600"
                    name="log-out"></ion-icon>
            </ion-list-header>
            <ion-list-header style="zoom:.8" color="primary" (click)="changeOrg()">
                Org: {{orgName}}
                <ion-badge *ngIf="alarmData.getAllAlarmsNotInOrg(orgId) > 0" item-right color='alarm0'
                    class="icon-badge">{{alarmData.getAllAlarmsNotInOrg(orgId)}}</ion-badge>
                <ion-icon item-right name='settings'
                    style="color: white; margin-right: 15px; margin-bottom: 2px; font-size: 3em;"></ion-icon>
            </ion-list-header>
            <ion-list-header *ngIf="!pushEnabled" style="zoom:.8" color="danger" (click)="goToPushSettings()">
                Notifications are disabled. Tap here to enable.
              
            </ion-list-header>
            <button ion-item menuClose *ngFor="let p of appPages" (click)="openPage(p)">
                <ion-icon item-start [name]="p.icon" [color]="isActive(p)"></ion-icon>
                {{p.title}}
            </button>
        </ion-list>
        <ion-toolbar>
            <ion-title>Sites</ion-title>
        </ion-toolbar>
        <!-- <ion-content> -->
        <ion-list>
            <ion-item no-padding class="site" *ngFor="let site of sites | async">
                <button detail='false' mode='md' ion-item (click)="toggleSite(site)">
                    <span *ngIf="isSiteSelected(site)">
                        <ion-icon name="checkbox"></ion-icon>&nbsp;
                    </span>
                    <span *ngIf="!isSiteSelected(site)">
                        <ion-icon name="square-outline"></ion-icon>&nbsp;
                    </span>
                    <span>{{site.siteName}}</span>
                </button>
                <button (click)="navToSiteAlarms(site)"
                    style="margin: 0px auto; height: 100%; padding: 0px; width: 50px" no-padding ion-button clear
                    item-right>
                    <ion-badge color="alarm0" *ngIf="alarmData.getAlarmCounts(null, null, null, site.siteId).activeCount + alarmData.getAlarmCounts(null, null, null, site.siteId).acknowledgedCount > 0">
                        {{alarmData.getAlarmCounts(null, null, null, site.siteId).activeCount + alarmData.getAlarmCounts(null, null, null, site.siteId).acknowledgedCount}}
                    </ion-badge>
                    <ion-badge color="alarm3" style="color: white" *ngIf="alarmData.getAlarmCounts(null, null, null, site.siteId).silentCount > 0">
                        {{alarmData.getAlarmCounts(null, null, null, site.siteId).silentCount}}
                    </ion-badge>
                </button>
            </ion-item>
        </ion-list>
        <!-- </ion-content> -->
        <!-- <ion-list style=" position: fixed; left: 0; bottom: 0px; right: 0; margin-bottom: 0px;">
                <button ion-item menuClose *ngFor="let p of loggedInPages" (click)="openPage(p)">
                    <ion-icon item-start [name]="p.icon" [color]="isActive(p)"></ion-icon>
                    {{p.title}}
                </button>
            </ion-list> -->
        <!-- <ion-list>
                <ion-list-header>
                    Tutorial
                </ion-list-header>
                <button ion-item menuClose (click)="openTutorial()">
                    <ion-icon item-start name="hammer"></ion-icon>
                    Show Tutorial
                </button>
            </ion-list> -->
    </ion-content>
</ion-menu>

<!-- <ion-menu id="sitesList" side="right" [content]="content">
        
    </ion-menu> -->

<ion-nav [root]="rootPage" #content swipeBackEnabled="true" main name="app"></ion-nav>

<!-- </ion-split-pane> -->