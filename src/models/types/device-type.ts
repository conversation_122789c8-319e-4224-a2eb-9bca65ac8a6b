export enum DeviceType {
    AbstractDevice                          = 0x00000000,
        DualRelayCardBooleanFanDevice           = 0x00000001, //deprecated
        OctoRelayCardBooleanFanDevice           = 0x00000002, //deprecated
        DualRelayCardBooleanFoggerDevice        = 0x00000003, //deprecated
        QuadRelayCardBooleanFanDevice           = 0x00000004, //deprecated
        QuadRelayCardBooleanFoggerDevice        = 0x00000005, //deprecated
        QuadRelayCardBooleanHeaterDevice        = 0x00000006, //deprecated
        QuadRelayCardBooleanMotorDevice         = 0x00000007, //deprecated
        AlarmDevice                             = 0x00000008,
        ClockDevice                             = 0x00000009,
        BooleanRelayMotorDevice                 = 0x0000000A,
        BooleanRelayFoggerDevice                = 0x0000000B,
        BooleanRelayHeaterDevice                = 0x0000000C,
        BooleanRelayFanDevice                   = 0x0000000D,
        VariableCardVariableFanDevice           = 0x00000010,
        VariableCardHeatLampDevice              = 0x00000011,
        DualRelayCardBooleanMotorDevice         = 0x00000100, //deprecated
        DualRelayCardBooleanHeaterDevice        = 0x00010000, //deprecated
        OctoRelayCardBooleanHeaterDevice        = 0x00020000, //deprecated
        VariableCurrentCardVariableHeaterDevice = 0x00100000,
        VariableCurrentCardVariableFanDevice    = 0x00200000,
        CurtainCardCurtainDevice                = 0x01000000,
        CurtainDeviceV2                         = 0x01000001,
        LightDevice                             = 0x02000000,
        SlideDevice                             = 0x04000000,
        BallDropDevice                          = 0x08000000,
        VariableDoserDevice                     = 0x09000000,
        BooleanDoserDevice                      = 0x0A000000,
        BooleanValveDevice                      = 0x0B000000,
        GenericBooleanMotorDevice               = 0x0C000000,
        VirtualDeviceMask                       = 0x10000000, //virtual devices will have lhs bit high
        SingleSmsDevice                         = 0x10000001,
        CycleTimerDevice                        = 0x10000002, //deprecated
        PercentCycleTimerDevice                 = 0x10000003, //deprecated
        SwitchDevice                            = 0x10000004,
        DayTimerDevice                          = 0x10000006, //deprecated
        ChainDiskDevice                         = 0x10000007,
        FanGroupDevice                          = 0x10000008,
        BinSlideDevice                          = 0x10000009,
        GenericProcessDevice                    = 0x1000000A,
        MixingProcessDevice                     = 0x1000000B,
        BatchTankProcessDevice                  = 0x1000000C,
        RemoteDevice                            = 0x40000000  //Remote devices start from here
}