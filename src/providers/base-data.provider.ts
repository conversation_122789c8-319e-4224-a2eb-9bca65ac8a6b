import { Observable, BehaviorSubject } from 'rxjs';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { ConfigurationService } from '../services/configuration.service';
import { OrgContextService } from '../services/org-context.service';

export class BaseDataProvider<T> {

    protected orgId: number;
    protected configurations = ConfigurationService;

    public data: Observable<T>;
    public _data$: BehaviorSubject<T>;
    protected dataStore: {
        values: T;
    }

    constructor(protected http: HttpClient, orgContext: OrgContextService) {

        orgContext.OrganizationChanged.subscribe((orgIdEmit) => {
            this.orgId = orgIdEmit;
        });
    }

    public getData<T>(url): Observable<T> {
        return this.http.get<T>(url)
            .catch((err) => this.handleError(err));
    }

    public getDataUncaught<T>(url): Observable<T> {
        return this.http.get<T>(url)
    }

    public postData<T>(url, postBody): Observable<T> {
        return this.http.post<T>(url, postBody)
            .catch((err) => this.handleError(err));
    }

    public deleteData<T>(url) : Observable<T> {
        return this.http.delete<T>(url)
            .catch((err) => this.handleError(err));
    }

    public getRequestHeaders(): { headers: HttpHeaders | { [header: string]: string | string[]; } } {
        return null;
    }


    protected handleError(error: Response | any) {
        let errMsg: string;
        if (error instanceof Response) {
            const err = error || '';
            errMsg = `${error.status} - ${error.statusText || ''} ${err}`;
        } else {
            errMsg = error.message ? error.message : error.toString();
        }
        console.error(errMsg);
        return Observable.throw(error);
    }
}