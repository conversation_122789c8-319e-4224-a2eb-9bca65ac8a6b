import { BaseDataProvider } from "./base-data.provider";
import { ConfigurationService } from "../services/configuration.service";
import { HttpClient } from "@angular/common/http";
import { OrgContextService } from "../services/org-context.service";
import { Observable } from "rxjs";
import { OfflineAlert } from "../models/offline-alert.model";
import { Injectable } from "@angular/core";

@Injectable()
export class OfflineAlertDataProvider extends BaseDataProvider<OfflineAlert[]> {
    private offlineAlertCacheKey: string = "offline-alert-list";
    private configuration = ConfigurationService;
    private baseUrl: string = ConfigurationService.baseUrl;

    constructor(http: HttpClient, private orgService: OrgContextService) {
        super(http, orgService);
    }

    private readonly _offlineAlertsUrl: string = "api/mobile/alerts";

    private get offlineAlertsUrl(): string { return this.baseUrl + this._offlineAlertsUrl; }

    public getOfflineAlerts<T>(): Observable<OfflineAlert[]> {
        return this.getOfflineAlertsFromServer();
    }

    public getOfflineAlertsFromServer<T>(): Observable<OfflineAlert[]> {
        return this.getData<OfflineAlert[]>(this.offlineAlertsUrl);
    }



}