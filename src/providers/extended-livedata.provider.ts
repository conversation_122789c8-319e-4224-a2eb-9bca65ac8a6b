import { BaseDataProvider } from "./base-data.provider";
import { HttpClient } from "@angular/common/http";
import { OrgContextService } from "../services/org-context.service";
import { String } from 'typescript-string-operations';
import { Injectable } from "@angular/core";
import { Observable } from "../../node_modules/rxjs";

@Injectable()
export class ExtendedLiveDataProvider extends BaseDataProvider<any>{

    private readonly _extendedLiveDataUrl: string = "/api/extendedlivedata/{0}/{1}";
    private get extendedLiveDataUrl(): string { return this.configurations.baseUrl + this._extendedLiveDataUrl; }


    constructor(http: HttpClient, private orgContext: OrgContextService) {
        super(http, orgContext);
        this.orgId = 1;  // TODO:  Temporary until you can select the actual org that the user has access too
    }

    public requestExtendedLiveValues(serialNumber, roomIndex): Observable<any> {
        var urlSegment = String.Format(this.extendedLiveDataUrl, serialNumber, roomIndex);
        console.debug('Requesting Extended Data For ::-> ', serialNumber, roomIndex);
        return this.getData(urlSegment);
    }


}