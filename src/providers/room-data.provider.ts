import { BaseDataProvider } from "./base-data.provider";
import { Injectable } from "@angular/core";
import { String } from 'typescript-string-operations';
import { OrgContextService } from "../services/org-context.service";
import { HttpClient } from "@angular/common/http";
import { notImplemented } from "@angular/core/src/render3/util";
import { Observable } from "rxjs/Observable";
import { Room } from "../models/room-model";
import { Sensor } from "../models/sensor-model";
import { Device } from "../models/device-model";
import { Control } from "../models/controls-model";
import * as _ from 'lodash';

@Injectable()
export class RoomDataProvider extends BaseDataProvider<Room[]> {

    public roomsListcacheKey: string = "rooms-list-cache";
    public partial_sensorCacheKey: string = "sensors-cache-{0}";
    public partial_deviceCacheKey: string = "devices-cache-{0}";

    private readonly _roomsUrl: string = "/api/dashboard/1/site/205";
    private readonly _roomsForControl: string = `/api/rooms/{0}`; // controlId
    private readonly _devicesForRoom: string = `/api/mobile/device/{0}/groupbyroom`; // controlId;
    private readonly _sensorsForRoom: string = `/api/mobile/sensors/{0}/groupbyroom`; // controlId;

    private entityMap: Map<string, Sensor & Device>;
    public controlsPulled: Map<string, boolean>;
    private checkingMap: Map<string, boolean>

    private getUrl(url, parameter): string {
        console.log('Get Url', url, parameter, this.configurations.baseUrl + String.Format(url, parameter));
        if(parameter) return this.configurations.baseUrl + String.Format(url, parameter);
        else return this.configurations.baseUrl + url;
    }

    constructor(http: HttpClient, orgContext: OrgContextService) {
        super(http, orgContext);
        this.entityMap = new Map<string, Sensor & Device>();
        this.checkingMap = new Map<string, boolean>();
        this.controlsPulled = new Map<string, boolean>();
    }

    public getRooms(): Observable<Room[]> {
        return this.getData(this.getUrl(this._roomsUrl, ''));

    }

    getEntity(hardwareId: string){
        if(!this.entityMap.get(hardwareId) && !this.checkingMap.get(hardwareId.split('.')[0])){
            this.checkingMap.set(hardwareId.split('.')[0], true)
            this.getSensorsForControl(hardwareId.split('.')[0])
            .catch(() => {
                return Observable.of(null);
            })
            .subscribe();
            this.getDevicesForControl(hardwareId.split('.')[0])
            .catch(() => {
                return Observable.of(null);
            })
            .subscribe();
            window.setTimeout(() =>  this.checkingMap.set(hardwareId.split('.')[0], false), 5000)
        }
        return this.entityMap.get(hardwareId);
    }

    public isSensorOrDevice(hardwareId){
        var entity = this.entityMap.get(hardwareId);
        if(!entity) return null;
        if(entity.sensorSerialNumber) return "sensor";
        else return "device";
    }

    public getSensorsForControl(serialNumber): Observable<any> {
        return this.getDataUncaught(this.getUrl(this._sensorsForRoom, serialNumber)).map((data: any) => {
            this.controlsPulled.set(serialNumber, true)
            data.forEach(r => {
                r.sensors.forEach(s => {
                    s.roomName = r.name;
                    this.entityMap.set(s.sensorSerialNumber, s);
                    
                });
                r.sensors = _.sortBy(r.sensors, 'sensorName');
            });
            data = _.sortBy(data, 'roomPlacementIndex')
            localStorage.setItem(`sensors_${serialNumber}`, JSON.stringify(data))
            return data;
        });
    }

    public getDevicesForControl(serialNumber): Observable<Device[]> {
        return this.getDataUncaught(this.getUrl(this._devicesForRoom, serialNumber)).map((data: any) => {
            this.controlsPulled.set(serialNumber, true)
            data.forEach(r => {
                r.devices.forEach(d => {
                    d.roomName = r.name;
                    this.entityMap.set(d.deviceSerialNumber, d);
                });
                r.devices = _.sortBy(r.devices, 'deviceName')
            })
            data = _.sortBy(data, 'roomPlacementIndex')
            localStorage.setItem(`devices_${serialNumber}`, JSON.stringify(data))
            return data;
        });
    }

}