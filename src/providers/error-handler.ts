import { IonicErrorHandler } from "ionic-angular";
import { Injector, Injectable, Error<PERSON>and<PERSON> } from "@angular/core";

@Injectable()
export class AppErrorHandler implements ErrorHandler {
    ionicErrorHandler: IonicErrorHandler;

    constructor(injector: Injector) {
        try {
            this.ionicErrorHandler = injector.get(IonicErrorHandler);
        } catch (e) {
            // unable to get the IonicErrorHandler provider, ensure
            // IonicErrorHandler has been added to the providers list
        }
    }

    handleError(err: any): void {
        // Remove this if you want to disable Ionic's auto exception handling
        // in development mode.
        this.ionicErrorHandler && this.ionicErrorHandler.handleError(err);
    }
}