import { Injectable, Injector } from "@angular/core";
import { ConfigurationService } from "../services/configuration.service";
import { HttpClient } from "@angular/common/http";
import { OrgContextService } from "../services/org-context.service";
import { BaseDataProvider } from "./base-data.provider";
import { String } from 'typescript-string-operations';
import { LocalStorageHelper } from "./storage-helper.provider";
import { Push } from "@ionic-native/push";


@Injectable()
export class PushRegistrationProvider extends BaseDataProvider<any> {

    // {0} = username
    // {1} = token from push
    private readonly _registerTokenUrl: string = '/api/devicetoken/{0}/{1}'; // [HTTPOST]
    private readonly _clearTokensUrl: string = '/api/devicetoken/{0}/{1}'; // [HTTPDELETE]

    private get registerTokenUrl(): string {
        return this.configurations.baseUrl + this._registerTokenUrl;
    }

    private get clearTokensUrl(): string {
        return this.configurations.baseUrl + this._clearTokensUrl;
    }

    private get username(): string {
        return this.storageHelper.getData('username');
    }

    private get token(): string {
        return this.storageHelper.getData('pushtoken');
    }

    constructor(http: HttpClient,
        orgContext: OrgContextService, private push: Push,
        private storageHelper: LocalStorageHelper) {

        super(http, orgContext);
    }

    public registerPushDevice<T>(): void {
        const endpointUrl = String.Format(this.registerTokenUrl, this.username, this.token);
        this.postData<T>(endpointUrl, null);
    }

    public clearRegistration<T>(): void {
        const endpointUrl = String.Format(this.registerTokenUrl, this.username, this.token);
        this.deleteData<T>(endpointUrl);
    }


}