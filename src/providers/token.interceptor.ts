import { Injectable, Injector } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse, HttpSentEvent, HttpHeaderResponse, HttpProgressEvent, HttpResponse, HttpUserEvent } from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { Observable } from 'rxjs/Observable';
import { BehaviorSubject } from 'rxjs';
import { AlertController, Events } from 'ionic-angular';
import { Token } from '@angular/compiler';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {

    isRefreshingToken: boolean = false;
    tokenSubject: BehaviorSubject<string> = new BehaviorSubject<string>(null);
    private auth = this.injector.get(AuthService);
    constructor(public injector: Injector, private alerts: AlertController, private events: Events) {
    }

    addToken(req: HttpRequest<any>, token: string): HttpRequest<any> {
        return req.clone({ setHeaders: { Authorization: 'Bearer ' + token } })
    }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpSentEvent | HttpHeaderResponse | HttpProgressEvent | HttpResponse<any> | HttpUserEvent<any>> {
        return next.handle(this.addToken(req, this.auth.getAuthToken()))
            .catch(error => {
                if (error instanceof HttpErrorResponse) {
                    switch ((<HttpErrorResponse>error).status) {
                        case 400:
                            return this.handle400Error(error);
                        case 401:
                            return this.handle401Error(req, next);
                        case 500:
                            return this.handle500Error()
                        case 0:
                            return this.handle0Error()
                    }
                } else {
                    return Observable.throw(error);
                }
            });
    }

    handle400Error(error) {
        if (error && error.status === 400 && (error.error.error === 'invalid_request' || error.error.error === 'invalid_grant'))  {
            // If we get a 400 and the error message is 'invalid_grant', the token is no longer valid so logout.
            let alert = this.alerts.create({
                title: 'Invalid Login',
                subTitle: error.error.error_description,
                buttons: ['Dismiss']
            });
            alert.present();
            return this.logoutUser();
        }
        return Observable.throw(error);
    }

    handle401Error(req: HttpRequest<any>, next: HttpHandler) {
        if (!this.isRefreshingToken) {
            this.isRefreshingToken = true;

            // Reset here so that the following requests wait until the token
            // comes back from the refreshToken call.
            this.tokenSubject.next(null);

            return this.auth.refreshToken()
                .switchMap((newToken: any) => {
                    this.auth.loginStatus$.next(true);
                    if (newToken) {
                        this.tokenSubject.next(newToken.access_token);
                        console.log("Retrying command with new token", newToken.access_token)
                        return next.handle(this.addToken(req, newToken.access_token));
                    }

                    // If we don't get a new token, we are in trouble so logout.
                    return Observable.throw("");
                })
                .catch(error => {
                    // If there is an exception calling 'refreshToken', bad news so logout.
                    return Observable.throw("");
                })
                .finally(() => {
                    this.isRefreshingToken = false;
                });
        } else {
            return this.tokenSubject
                .filter(token => token != null)
                .take(1)
                .switchMap(token => {
                    return next.handle(this.addToken(req, token));
                });
        }
    }

    handle500Error(){
        return Observable.throw(500);
    }

    handle0Error(){
        return Observable.throw(0);
    }


    logoutUser() {
        // Route to the login page (implementation up to you)
        this.events.publish('logout');
        // var deviceId = localStorage.getItem("DeviceId");
        // this.auth.loginStatus$.next(false);
        localStorage.clear();
        // if(deviceId) localStorage.setItem("DeviceId", deviceId);
        return Observable.throw("");
    }
}