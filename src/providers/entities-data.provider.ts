import { BaseDataProvider } from "./base-data.provider";
import { Injectable } from "@angular/core";
import { String } from 'typescript-string-operations';
import { OrgContextService } from "../services/org-context.service";
import { HttpClient } from "@angular/common/http";
import { notImplemented } from "@angular/core/src/render3/util";
import { Observable } from "rxjs/Observable";
import { Room } from "../models/room-model";
import { Sensor } from "../models/sensor-model";
import { Device } from "../models/device-model";
import { EntityType } from "../models/types/entity-type";
import { Entity } from "../models/entity.model";
import { DBKeys } from "../models/dbkeys.static";
import * as _ from 'lodash';

@Injectable()
export class EntitiesDataProvider extends BaseDataProvider<Entity[]> {

    private readonly _roomsUrl: string = "/api/mobile/rooms/{0}/{1}/{2}";
    private readonly _liveValuesByGroupUrl: string = '/api/mobile/livevalues/requestlivevaluelist/{0}';

    private getUrl(url, parameter): string {
        console.log('Get Url', url, parameter, this.configurations.baseUrl + String.Format(url, parameter));
        if(parameter) return this.configurations.baseUrl + String.Format(url, parameter);
        else return this.configurations.baseUrl + url;
    }

    constructor(http: HttpClient, orgContext: OrgContextService) {
        super(http, orgContext);
        this.dataStore = {values: []}
    }

    getEntities(searchTerm: string, entityType: EntityType): Observable<Entity[]>{
        if (searchTerm == "Bin") searchTerm = "Bins";
        console.log(this.configurations.baseUrl + String.Format(this._roomsUrl, entityType, searchTerm))
        return this.getData(this.configurations.baseUrl + String.Format(this._roomsUrl, localStorage.getItem(DBKeys.SELECTED_ORG_ID), entityType, searchTerm))
            .map((data: Entity[]) => {
                this.dataStore.values = _.sortBy(data, 'entityName');
                var entityIds = data.map(entity => {
                    return entity.entitySerialNumber;
                });
                this.getLiveValuesList(entityIds);
                return this.dataStore.values;
            }, error => {return Observable.of(null)});
    }

    public getLiveValuesList(entityIds: string[]) {
        var requestList = [];
        entityIds.forEach(id => {
            var split = id.split('.');
            var request = { serialNumber: split[0], cardIndex: split[1], slotIndex: split[2] }
            requestList.push(request);
        });
        this.postData(this.getUrl(this._liveValuesByGroupUrl, localStorage.getItem('username')), requestList).subscribe()
    }

}