
<div>
    <ion-segment padding [(ngModel)]="offlineAlertsSegment">
        <ion-segment-button value="active">active</ion-segment-button>
        <ion-segment-button value="cleared">cleared</ion-segment-button>
    </ion-segment>
</div>



<div [ngSwitch]="offlineAlertsSegment">
    <div *ngIf="!activeOfflineAlertsPresent">
        <ion-list *ngSwitchCase="'active'">
            <ion-list-header>
                No active offline alerts...
            </ion-list-header>
        </ion-list>
        <ion-list *ngSwitchCase="'cleared'">
            <ion-list-header>
               No cleared offline alerts...
            </ion-list-header>
        </ion-list>
    </div>
</div>

<div [ngSwitch]="offlineAlertsSegment">
    <div *ngIf="activeOfflineAlertsPresent">
    <ion-list >
        <ion-list-header>
            Offline Alerts
        </ion-list-header>
        <button ion-item *ngFor="let offlineAlert of offlineAlerts | async" style="zoom: .85">
            <ion-icon name="radio-button-on" style="zoom: 0.5; line-height:2.5em" [color]="offlineAlert.state === 0 ? 'danger' : 'primary'" item-start></ion-icon>
            <ion-grid>
                <ion-row>
                    <ion-col col-12 style="zoom:0.9">
                        {{offlineAlert.description}}
                    </ion-col>
                </ion-row>
                <ion-row>
                    <ion-col col-1>
                        <ion-icon name="bell" style="zoom:0.8"></ion-icon>
                    </ion-col>
                    <ion-col col-5 style="zoom:0.8; line-height:1.6">
                        {{offlineAlert.serialNumber}}
                    </ion-col>
                    <ion-col col-1>
                        <ion-icon name="contact" style="zoom:0.8"></ion-icon>
                    </ion-col>
                    <ion-col col-5 style="zoom:0.8; line-height:1.6">
                        {{offlineAlert.id}}
                    </ion-col>
                </ion-row>
            </ion-grid>
        </button>
    </ion-list>
</div>
</div>
