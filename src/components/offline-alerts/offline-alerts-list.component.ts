import { Component, Input } from "@angular/core";
import { Observable } from "rxjs";
import { OfflineAlert } from "../../models/offline-alert.model";
import { OfflineAlertDataProvider } from "../../providers/offine-alert-data.provider";

@Component({
    selector: 'offline-alerts-list',
    templateUrl: 'offline-alerts-list.component.html'
})
export class OfflineAlertsList {

    @Input() searchText: string = '';

    public offlineAlertsSegment: any = 'active';
    public activeOfflineAlertsPresent: boolean = false;
    public offlineAlerts: Observable<OfflineAlert[]>;

    constructor(protected offlineAlertProvider: OfflineAlertDataProvider) { }

    ionViewWillEnter() {
        this.getAlertData();
    }

    handleRefresh(event: any) {
        this.getAlertData().subscribe((unused) => {
            event.complete();
        }, error => { });
    }

    getAlertData(): Observable<OfflineAlert[]> {
        this.offlineAlerts = this.offlineAlertProvider.getOfflineAlerts<OfflineAlert[]>();
        this.offlineAlerts.subscribe(offlineAlerts => {
            console.log('Offline Alerts', offlineAlerts);
            this.activeOfflineAlertsPresent = offlineAlerts.filter(a => a.state <= 1).length > 0;
        });

        return this.offlineAlerts;
    }
}