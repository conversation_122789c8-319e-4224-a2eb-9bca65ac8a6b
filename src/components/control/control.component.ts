import { Component, EventEmitter, Input, Output } from "@angular/core";
import { Observable, Subscription } from "rxjs/Rx";
import { SiteContextService } from "../../services/site-context.service";
import { Control } from "../../models/controls-model";
import { AlarmDataProvider } from "../../providers/alarm-data.provider";
import * as moment from 'moment'
import { ControlDataProvider } from "../../providers/control-data.provider";
import { InAppBrowser, InAppBrowserEvent } from "@ionic-native/in-app-browser";
import { LoadingController, Platform, ToastController } from "ionic-angular";
import { SoftwareType } from "../../models/types/software-type";
import { ConfigurationService } from "../../services/configuration.service";
import { DBKeys } from "../../models/dbkeys.static";
import { AuthService } from "../../services/auth.service";
import { v } from "@angular/core/src/render3";

@Component({
  selector: 'control',
  templateUrl: 'control.component.html'
})
export class ControlComponent {

  @Input('control') control;
  @Input('searchText') searchText: string;
  @Output() controlClicked = new EventEmitter();
  @Output() toggleFavoriteClicked = new EventEmitter();

  text: string;
  protected liveValuesMap: Observable<Map<string, any>>;
  private sub: Subscription;

  constructor(public alarmData: AlarmDataProvider, private siteContext: SiteContextService, private controlData: ControlDataProvider, 
    private iab: InAppBrowser, private platform: Platform, private loadingCtrl: LoadingController, private auth: AuthService, private toastCtrl: ToastController) {

  }

  ngOnDestroy() {
    // this.sub.unsubscribe();
  }

  itemTapped(event, item) {
    this.controlClicked.emit();
    // console.log("Control Clicked")
  }

  favoriteClicked() {
    this.toggleFavoriteClicked.emit();
    // console.log("Favorite Clicked")
  }

  shouldShow(control: Control) {
    if (!this.siteContext.isSiteSelected(control.siteId)) return false;
    if (!this.searchText || this.searchText.trim() == "") return true;
    if (control.name == null || control.name == undefined) control.name = "";
    return (control.name.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
      control.serialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1);
  }

  isOnline(control: Control) {
    if (moment().diff(moment(control.lastHttpPing), 'minutes') > 5) {
      if (moment().diff(moment(control.lastMqttPing), 'minutes') > 5) {
        return 'offline';
      }
      return 'offline'
    }
    return 'online';
  }

  startRemoteAccess(control: Control) {
    if(this.isOnline(this.control) == 'offline') {
      this.toastCtrl.create({
        message: "Can't open connection as control is marked offline",
        position: 'middle',
        duration: 5000,
        showCloseButton: true
      }).present();
      return;
     }
    if(control.softwareType == SoftwareType.FUSION || control.softwareType == SoftwareType.UNKNOWN) {
      this.startVNC();
    }
    else if(control.softwareType == SoftwareType.FUSION_LIGHT) {
      this.startWebRemoteControlFusionLight(control.serialNumber)
    }
  }

  startWebRemoteControlFusionLight(serialNumber: number) {
  
    console.log(this.platform.is('cordova'))
    var url =  `${ConfigurationService.fusionLightWebUrl}?serialNo=${this.control.serialNumber}&authToken=${this.auth.getAuthToken()}`
    // if(this.platform.is('android')){
    //   var broswer = this.iab.create(url, '_self', 'location=no,toolbar=no');
    //   broswer.hide();
    //   broswer.close();
    // }
    if(this.platform.is('cordova')) {
      var browser = this.iab.create(url, '_system', 'location=no,toolbar=no');
      browser.on('loadstop').subscribe(() => {
        browser.close();
      })
      browser.close();
      browser.close();
    }
    else window.open(url);
  }



  startVNC() {
     if(this.isOnline(this.control) == 'offline') {
      this.toastCtrl.create({
        message: "Can't open connection as control is marked offline",
        position: 'middle',
        duration: 5000,
        showCloseButton: true
      }).present();
      return;
     }
     if(this.platform.is('cordova')){
      var loader = this.loadingCtrl.create(
        {content: 'Starting Remote VNC connection'}
        );
        loader.present();
        this.controlData.startVnc(this.control.controlId)
        .timeout(10000)
        .catch((err) => loader.dismiss())
        .subscribe((port: any) => {
          var connectionUrl = `http://flightline-control.com/assets/novnc/vnc.html?host=*************&port=7000&password=ou812vncfusionconnect&path=%2Fwebsockify%3Ftoken%3D${port.tokenPort}&autoconnect=1&resize=none`;
          setTimeout(() => {
            //console.log("replacing with conneciton url")
            loader.dismiss();
            this.iab.create(connectionUrl);
          }, 4000);
        });
    }
    else {
      const newWindow = window.open("/", '_blank', 'width=1070,height=830,toolbar=0,menubar=0;location=0');
    
  var url = `http://flightline-control.com/assets/novnc/vnc.html?connecting=true`;
      newWindow.location.replace(url);
      setTimeout(() => {
        //console.log("replacing with middleman url")
        var url = `http://flightline-control.com/assets/novnc/vnc.html?connecting=true`;
        newWindow.location.replace(url);
        this.controlData.startVnc(this.control.controlId).subscribe((port: any) => {
          var connectionUrl = `http://flightline-control.com/assets/novnc/vnc.html?host=*************&port=7000&password=ou812vncfusionconnect&path=%2Fwebsockify%3Ftoken%3D${port.tokenPort}&autoconnect=1&resize=scale`;
          setTimeout(() => {
            //console.log("replacing with conneciton url")
            newWindow.location.replace(connectionUrl);
          }, 4000);
        });
      }, 500)
    }
  }

  shouldShowBadge(control: Control) {
    var ac = this.alarmData.getAlarmCounts(control.serialNumber, null, null);
    if (ac.acknowledgedCount + ac.activeCount == 0) return false;
    return true;
  }

  shouldShowSilentBadge(control: Control) {
    var ac = this.alarmData.getAlarmCounts(control.serialNumber, null, null);
    if (ac.silentCount == 0) return false;
    return true;
  }

  getLVColor(value) {
    if (value) return 'white';
    return 'orange'
  }
}
