<div class="control-item" ion-item style="zoom:.85" *ngIf="shouldShow(control)">
    <ion-icon name="radio-button-on" style="zoom:0.5; line-height:2.5em" [color]="isOnline(control)" item-start>
    </ion-icon>
    <ion-grid style="padding: 0px">
      <ion-row style="height: 100%; padding: 0px;">
        <ion-col style="margin-top: 15px;margin-bottom: 15px;" col-1 (click)="favoriteClicked(control)">
            <ion-icon style="font-size: 1.4em" class="pushDown" [name]="control.isFavorite ? 'star' : 'star-outline'"></ion-icon>
        </ion-col>
        <ion-col style="margin-top: 15px;margin-bottom: 15px;" (click)="itemTapped()" col-6>
          <h2>{{ control.name || control.serialNumber }}</h2>
          <p class="version">{{ control.siteSiteName}} - Version: {{control.version}}</p>
        </ion-col>
        <ion-col style="margin-top: 15px;margin-bottom: 15px;" (click)="itemTapped()" col-1></ion-col>
        <ion-col style="margin-top: 15px;margin-bottom: 15px;" (click)="itemTapped()" col-2>
          <ion-badge *ngIf="shouldShowBadge(control)" class="pushdown-badge" color="danger">
            {{ alarmData.getAlarmCounts(control.serialNumber, null).activeCount + alarmData.getAlarmCounts(control.serialNumber, null).acknowledgedCount }}
          </ion-badge>
          <ion-badge *ngIf="shouldShowSilentBadge(control)" class="pushdown-badge control-alarm-silent">
            {{ alarmData.getAlarmCounts(control.serialNumber, null).silentCount }}
          </ion-badge>
        </ion-col>
        <ion-col style="padding: 0px; margin: 0px" col-2>
          <button style="height:100%; margin:0px" ion-button block full (click)="startRemoteAccess(control)">
            VNC
          </button>
        </ion-col>
      </ion-row>
    </ion-grid>
</div>