<!-- <button > -->
    <ion-row (tap)="deviceTapped(device)" *ngIf="device && shouldShow() && shouldShowMode()">
        <ion-col col-1 align-self-center>
          <ion-icon [name]="device.deviceType | DeviceDisplayIcon: [device.deviceOrSensorType]" item-start></ion-icon>
        </ion-col>
        <ion-col align-self-center text-left col-5 style="max-width: 60%">
          <div class="entityName">
            {{ device.entityName }}
          </div>
          <div class="roomName" style="overflow:hidden; text-overflow:ellipsis">
            {{ device.roomName }} - {{ device.controlName || device.controlSerialNumber }}
          </div>
        </ion-col>
        
        <ion-col col-1 align-self-center text-right>
          <!-- <div *ngIf="(alarmCount | async)?.get(device.entitySerialNumber)?.activeCount > 0 || (alarmCount | async)?.get(device.entitySerialNumber)?.ackCount > 0">
            <ion-badge *ngIf="(alarmCount | async)?.get(device.entitySerialNumber)?.activeCount > 0" color="alarm0">{{(alarmCount | async)?.get(device.entitySerialNumber)?.activeCount}}</ion-badge>
          </div>
          <div>
            <ion-badge *ngIf="(alarmCount | async)?.get(device.entitySerialNumber)?.ackCount > 0" color="alarm1">{{(alarmCount | async)?.get(device.entitySerialNumber)?.ackCount}}</ion-badge>
          </div> -->
        </ion-col>
        <ion-col col-5 align-self-center text-right>
          <span [ngStyle]="{'color': getLVColor((liveValuesMap | async)?.get(device.entitySerialNumber)?.value)}">
            {{ ((liveValuesMap | async )?.get(device.entitySerialNumber)?.value | LiveValueDisplay: ['device',
              device.deviceOrSensorType, controlData.getControl(device.controlSerialNumber)]) || '- -'}}
            <div class="mode" *ngIf="device.deviceType < 268435456">
              {{  (liveValuesMap | async )?.get(device.entitySerialNumber)?.mode 
                | ModeDisplay: [ (liveValuesMap | async )?.get(device.entitySerialNumber)?.mode] }} 
                - {{ (liveValuesMap | async )?.get(device.entitySerialNumber)?.amps | number: '1.0-3' }} amps
            </div>
          </span>
        </ion-col>
      </ion-row>
      <!-- </button> -->
      
      
      
      <!-- <button > -->
      <ion-row  (tap)="sensorTapped(sensor)" *ngIf="sensor && shouldShow()" style="width: 100%">
        <ion-col col-1 align-self-center style="width: 8%">
          <ion-icon [name]="sensor.sensorType | SensorDisplayIcon: [sensor.deviceOrSensorType]"></ion-icon>
        </ion-col>
        <ion-col align-self-center text-left col-6>
          <div class="entityName">
            {{ sensor.entityName }}
          </div>
          <div class="roomName" style="overflow:hidden; text-overflow:ellipsis">
            {{ sensor.roomName }} - {{ sensor.controlName || sensor.controlSerialNumber}}
          </div>
        </ion-col>
        <ion-col col-1 align-self-center text-right>
          <!-- <div *ngIf="(alarmCount | async)?.get(sensor.entitySerialNumber)?.activeCount > 0 || (alarmCount | async)?.get(sensor.entitySerialNumber)?.ackCount > 0">
            <ion-badge *ngIf="(alarmCount | async)?.get(sensor.entitySerialNumber)?.activeCount > 0" color="alarm0">{{(alarmCount | async)?.get(sensor.entitySerialNumber)?.activeCount}}</ion-badge>
          </div>
          <div>
            <ion-badge *ngIf="(alarmCount | async )?.get(sensor.entitySerialNumber)?.ackCount > 0" color="alarm1">{{(alarmCount | async)?.get(sensor.entitySerialNumber)?.ackCount}}</ion-badge>
          </div> -->
        </ion-col>
        <ion-col col-4 align-self-center text-right>
          <span [ngStyle]="{'color': getLVColor((liveValuesMap | async)?.get(sensor.entitySerialNumber)?.value)}">
           {{ ((liveValuesMap | async )?.get(sensor.entitySerialNumber)?.value | LiveValueDisplay: ['sensor',
              sensor.deviceOrSensorType, controlData.getControl(sensor.controlSerialNumber), (liveValuesMap | async )?.get(sensor.entitySerialNumber)?.label]) || '- -' }}
          </span>
        </ion-col>
      </ion-row>
      <!-- </button> -->