import { Component, Input } from "@angular/core";
import { Observable, Subscription } from "rxjs/Rx";
import { AlarmCount } from "../../models/alarm-count.model";
import { LiveValueDisplay } from "../../models/types/live-value-display";
import { ModeDisplayType } from "../../models/types/mode-display-type";
import { Mode } from "../../models/types/mode";
import { LiveValueService } from "../../services/live-value.service";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { SiteContextService } from "../../services/site-context.service";
import { SensorType } from "../../models/types/sensor-type";
import { DeviceType } from "../../models/types/device-type";
import { ActionSheetController, NavController, ToastController } from "ionic-angular";
import { RemoteSettingCommandType } from "../../models/types/remote-settings-command-type";
import { EntityType } from "../../models/types/entity-type";
import { Control } from "../../models/controls-model";

@Component({
  selector: 'entity',
  templateUrl: 'entity.component.html'
})
export class EntityComponent {

  @Input('sensor') sensor;
  @Input('device') device;
  @Input('searchText') searchText: string;
  @Input('alarmCount') alarmCount: Observable<Map<string, AlarmCount>>;
  @Input('liveValueDisplay') liveValueDisplay: LiveValueDisplay;
  @Input('modeType') modeType: ModeDisplayType;

  text: string;
  protected liveValuesMap: Observable<Map<string, any>>;
  private control: Control;
  private sub: Subscription;

  constructor(private liveValuesService: LiveValueService, public controlData: ControlDataProvider, private siteContext: SiteContextService, private actionSheet: ActionSheetController,
            private navCtrl: NavController, private toastCtrl: ToastController) {
    this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    this.sub = this.liveValuesMap.subscribe(map => {
      var data;
      if (this.sensor) {
        data = map.get(this.sensor.entitySerialNumber);
        if (data) this.sensor.liveValueData = data.value;
      }
      if (this.device) {
        data = map.get(this.device.entitySerialNumber);
        if (data) {
          this.device.liveValueData = data.value;
          this.device.mode = data.mode;
        }
      }
    });
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  itemTapped(event, item) {
    console.log(item);
    // this.quickNav.configureMenu(item.entityType, item);
    // this.quickNav.showMenu();
  }


  shouldShow() {
    if(this.sensor){
      if(!this.siteContext.isSiteSelected(this.sensor.siteId)) return false;
      if(this.sensor.deviceOrSensorType == SensorType.RemoteSensor) return false;
    }
    if(this.device){
      if(!this.siteContext.isSiteSelected(this.device.siteId)) return false;
      if(this.device.deviceOrSensorType == DeviceType.RemoteDevice) return false;
    }
    if (!this.searchText || this.searchText.trim() == "") return true;

    if (this.sensor) {
      return (this.sensor.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.sensor.entitySerialNumber.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.sensor.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.sensor.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.sensor.controlSerialNumber.indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }

    if (this.device) {
      return (this.device.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.device.entitySerialNumber.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.device.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.device.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
        this.device.controlSerialNumber.indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
  }

  sensorTapped(sensor){
    this.controlData.getControlBySerialNo(this.sensor.serialNo || this.device.serialNo).subscribe((ctrl) => {
      this.control = ctrl;
    })
    this.actionSheet.create({
      title: "Sensor Navigation",
      buttons: [
          {
              text: 'Alarms',
              handler: () => {
                  this.navCtrl.push('entity-alarms-page', sensor);
              }
          },
          {
              text: 'Graph',
              handler: () => {
                  if(this.control.fusionStatus < 13){
                    this.toastCtrl.create({
                      message: "Can't open connection as control is marked offline",
                      position: 'middle',
                      duration: 5000,
                      showCloseButton: true
                    }).present();
                    return;
                  }
                  this.navCtrl.push('entity-graphs-page', sensor);
              }
          },                
          // {
          //     text: 'Sensor Settings',
          //     handler: () => {                        
          //         var sensorData: any = {entityNumber: sensor.sensorSerialNumber, roomId: sensor.controlSerialNumber + "." + sensor.roomProgramId, controlSerialNumber: sensor.controlSerialNumber, controlName: this.control.name, deviceName: sensor.sensorName, remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Sensor }
          //         console.log('Navigating to sensor settings', sensorData);
          //         this.navCtrl.push('remote-settings-page', sensorData);
          //     }
          // },
          {
              text: 'Alarm Settings',
              handler: () => {    
                if(this.control.fusionStatus < 13){
                  this.toastCtrl.create({
                    message: "Can't request settings as control is marked offline",
                    position: 'middle',
                    duration: 5000,
                    showCloseButton: true
                  }).present();
                  return;
                }                      
                  var sensorData: any = {entityNumber: sensor.entitySerialNumber, roomId: sensor.controlSerialNumber + "." + sensor.programId, controlSerialNumber: sensor.controlSerialNumber, controlName: sensor.controlName, deviceName: sensor.entityName, remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Sensor }
                  console.log('Navigating to Sensor alarm settings', sensorData);
                  this.navCtrl.push('remote-settings-page', sensorData);
              }
          },                
          {
              text: 'Cancel',
              role: 'cancel',
          }
      ]
  }).present();
  }

  deviceTapped(device){
    this.actionSheet.create({
      title: "Device Navigation",
      buttons: [
          {
              text: 'Alarms',
              handler: () => {
                  this.navCtrl.push('entity-alarms-page', device);
              }
          },
          {
              text: 'Remote Control',
              handler: () => {
                  console.log("Nav to remote control", device)
                  this.navCtrl.push('remote-control-page', device);
              }
          },
          {
              text: 'Device Settings',
              handler: () => {     
                if(this.control.fusionStatus < 13){
                  this.toastCtrl.create({
                    message: "Can't request settings as control is marked offline",
                    position: 'middle',
                    duration: 5000,
                    showCloseButton: true
                  }).present();
                  return;
                }                        
                  var deviceData: any = {entityNumber: device.entitySerialNumber, roomId: device.controlSerialNumber + "." + device.programId, controlSerialNumber: device.controlSerialNumber, controlName: device.controlName, deviceName: device.entityName, remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Device }
                  console.log("Nav to device settings", deviceData);
                  this.navCtrl.push('remote-settings-page', deviceData);
              }
          },                
          {
              text: 'Alarm Settings',
              handler: () => {
                if(this.control.fusionStatus < 13){
                  this.toastCtrl.create({
                    message: "Can't request settings as control is marked offline",
                    position: 'middle',
                    duration: 5000,
                    showCloseButton: true
                  }).present();
                  return;
                }     
                  var deviceData: any = {entityNumber: device.entitySerialNumber, roomId: device.controlSerialNumber + "." + device.programId, controlSerialNumber: device.controlSerialNumber, controlName: device.controlName, deviceName: device.entityName, remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Device }                        
                  console.log("Nav to Device alarm settings", deviceData);
                  this.navCtrl.push('remote-settings-page', deviceData);
              }
          },                
          {
              text: 'Cancel',
              role: 'cancel',
          }
      ]
  }).present();
  }

  shouldShowMode() {
    if (this.modeType == null) return true;
    if (this.modeType == ModeDisplayType.All) return true;
    if (this.modeType == ModeDisplayType.ManualOn && this.device.mode == Mode.Manual && this.device.liveValueData != 0) return true;
    if (this.modeType == ModeDisplayType.ManualOff && ((this.device.mode == Mode.Manual && this.device.liveValueData == 0) || this.device.mode == Mode.Stop)) return true;
    if (this.modeType == ModeDisplayType.Auto && this.device.mode == Mode.Auto) return true;
    else return false;
  }

  getLVColor(value){
    if(value) return 'white';
    return 'orange'
}
}
