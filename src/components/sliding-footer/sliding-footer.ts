import { Component, Input, ViewChild, Output, EventEmitter } from "@angular/core";
import { RoomSearchType } from "../../models/types/room-search-type";
import { LiveValueDisplay } from "../../models/types/live-value-display";
import { ButtonsDisplayType } from "../../models/types/buttons-display-type";
import { ModeDisplayType } from "../../models/types/mode-display-type";
import { Gesture } from "ionic-angular";

/*
  Generated class for the SlidingFooterComponent component.

  See https://angular.io/docs/ts/latest/api/core/index/ComponentMetadata-class.html
  for more info on Angular 2 Components.
*/
@Component({
  selector: 'sliding-footer',
  templateUrl: 'sliding-footer.html'
})
export class SlidingFooterComponent {

  @Input('searchType') searchType: RoomSearchType;
  @Input('liveValueDisplay') liveValueDisplay: LiveValueDisplay;
  @Input('buttonsDisplayType') buttonsDisplayType: ButtonsDisplayType;
  @Input('modeType') modeType: ModeDisplayType;

  @ViewChild('footerContainer') footerContainer;
  @ViewChild('azButton') azButton;
  @ViewChild('valButton') valButton;
  @ViewChild('highButton') highButton;
  @ViewChild('lowButton') lowButton;

  @Output() sortAZ  = new EventEmitter();
  @Output() sortVal = new EventEmitter();
  @Output() showMode = new EventEmitter();
  @Output() showValues = new EventEmitter();

  private leftMargin;
  private numButtons = 4;
  gesture: Gesture;

  constructor() {
    this.leftMargin = -1;
  }

  ngOnInit(){
    this.gesture = new Gesture(this.footerContainer.nativeElement);
    this.gesture.listen();
    this.gesture.on('pan', (e) => {
      if(e.additionalEvent == "panleft"){
        this.leftMargin -= Math.abs(e.velocityX * 4.5);
        if(this.leftMargin < 25 * (this.numButtons - 4)) this.leftMargin = 25 * (this.numButtons - 4);
      }
      if(e.additionalEvent == "panright"){
        this.leftMargin += Math.abs(e.velocityX * 4.5);
        if(this.leftMargin > 0) this.leftMargin = 0;
      }
    });
  }

  sortAlphabetical() {
    this.azButton.color = 'primary';
    this.valButton.color = 'inactive';

    this.sortAZ.emit();
  }

  sortValue() {
    this.azButton.color = 'inactive';
    this.valButton.color = 'primary';
    
    this.sortVal.emit();
  }

  getColor(type: LiveValueDisplay){
    if(type == this.liveValueDisplay) return 'primary';
    return 'inactive';
  }

  displayLiveValues(){
    this.showValues.emit();
  }

  displayMode(){
    this.showMode.emit();
  }

  get24High() {
    this.highButton.color = 'color';
    this.lowButton.color = 'inactive';
  }

  get24Low() {
    this.highButton.color = 'inactive';
    this.lowButton.color = 'primary';
  }

}
