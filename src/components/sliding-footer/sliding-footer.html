<div class="outer-container" [ngStyle]="{'width.%': (100 + 25* (numButtons - 4))}">
    <div #footerContainer class="footer-container" [ngStyle]="{'left.%': leftMargin}">
      <ion-row style="bottom: 0px; padding: 0px; margin: 0px; position: absolute; height: 100%; width: 100%">
        <ion-col class="button-col">
          <button #azButton class="footer-button" ion-button large clear full color='inactive' (tap)="sortAlphabetical()">
            A-Z
            <ion-icon *ngIf="searchType != 1" name="arrow-round-down"></ion-icon>
            <ion-icon *ngIf="searchType == 1" name="arrow-round-up"></ion-icon>          
          </button>
        </ion-col>
        <ion-col class="button-col">
          <button #valButton class="footer-button" ion-button large clear full color='inactive' (tap)="sortValue()">
            Val
            <ion-icon *ngIf="searchType != 3" name="arrow-round-down"></ion-icon>          
            <ion-icon *ngIf="searchType == 3" name="arrow-round-up"></ion-icon>
          </button>
        </ion-col>
        <ion-col class="button-col" *ngIf="buttonsDisplayType == 2">
          <button #lvButton class="footer-button" ion-button large clear full [color]='getColor(0)' (tap)="displayLiveValues()">
            LiveValue
          </button>
        </ion-col>
        <ion-col class="button-col" *ngIf="buttonsDisplayType == 2 && (modeType == 0 || modeType == null)">
          <button #modeButton class="footer-button" ion-button large clear full [color]='getColor(1)' (tap)="displayMode()">
            Mode<br />
            All
          </button>
        </ion-col>
        <ion-col class="button-col" *ngIf="buttonsDisplayType == 2 && modeType == 1">
          <button #modeButton class="footer-button" ion-button large clear full [color]='getColor(1)' (tap)="displayMode()">
            Mode<br />
            On
          </button>
        </ion-col>
        <ion-col class="button-col" *ngIf="buttonsDisplayType == 2 && modeType == 2">
          <button #modeButton class="footer-button" ion-button large clear full [color]='getColor(1)' (tap)="displayMode()">
            Mode<br />
            Off
          </button>
        </ion-col>
        <ion-col class="button-col" *ngIf="buttonsDisplayType == 2 && modeType == 3">
          <button #modeButton class="footer-button" ion-button large clear full [color]='getColor(1)' (tap)="displayMode()">
            Mode<br />
            Auto
          </button>
        </ion-col>
      </ion-row>
    </div>
  </div>