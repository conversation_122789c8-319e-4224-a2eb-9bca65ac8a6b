import { Component, Input } from '@angular/core';
import { AlarmDataProvider } from '../../providers/alarm-data.provider';
@Component({
  selector: 'alarm-count',
  templateUrl: 'alarm-count.html'
})
export class AlarmCountComponent {
  @Input('orgId') orgId: number;

  constructor(public alarmData: AlarmDataProvider) {
  }

  ngOnInit(){
      if(!this.orgId) this.alarmData.getMinimalAlarms();
  }

}
