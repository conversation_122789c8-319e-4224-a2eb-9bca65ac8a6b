
<ion-item-sliding *ngIf="shouldShow()">
        <ion-item text-wrap style="white-space: normal" *ngIf="setting.displayValue != 'On' && setting.displayValue != 'Off'" (tap)="itemTapped(setting)">
          {{ setting.entityName }} {{ setting.direction }} than {{ setting.displayValue }}
          <ion-icon large item-right *ngIf="setting.active" name="notifications" color="online"></ion-icon>
          <ion-icon large item-right *ngIf="!setting.active" name="notifications" color="inactive"></ion-icon>
        </ion-item>
       
        <ion-item text-wrap style="white-space: normal" *ngIf="setting.displayValue == 'On' || setting.displayValue == 'Off'" (tap)="itemTapped(setting)">
          <ion-label> {{ setting.entityName }} turns {{ setting.displayValue}} </ion-label>
          <ion-icon large item-right *ngIf="setting.active" name="notifications" color="online"></ion-icon>
          <ion-icon large item-right *ngIf="!setting.active" name="notifications" color="inactive"></ion-icon>
        </ion-item>
        <ion-item-options side="right">
          <button ion-button icon-only (click)="deleteSetting(setting)" color="offline">
          <ion-icon name="trash"></ion-icon>
        </button>
        </ion-item-options>
       </ion-item-sliding> 