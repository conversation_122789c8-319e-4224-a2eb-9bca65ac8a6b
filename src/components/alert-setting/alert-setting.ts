
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { AlertSetting } from '../../models/alert-setting.model';

/*
  Generated class for the AlertSettingComponent component.

  See https://angular.io/docs/ts/latest/api/core/index/ComponentMetadata-class.html
  for more info on Angular 2 Components.
*/
@Component({
  selector: 'alert-setting',
  templateUrl: 'alert-setting.html'
})
export class AlertSettingComponent {

  @Input('setting') setting: AlertSetting;
  @Input('searchText') searchText: string;
  @Output() settingTapped = new EventEmitter();
  @Output() delete = new EventEmitter();

  constructor() {
  }

  itemTapped(setting: AlertSetting){
    this.settingTapped.emit();
  }

  deleteSetting(setting){
    this.delete.emit(setting);
  }

  shouldShow(): boolean {
    if (!this.searchText || this.searchText.trim() == "") return true;
    return ((this.setting.direction ? this.setting.direction.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.controlName ? this.setting.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.controlSerialNumber ? this.setting.controlSerialNumber.indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.entityName ? this.setting.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.setValue ? this.setting.setValue.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.entityId ? this.setting.entityId.indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
      || (this.setting.displayValue ? this.setting.displayValue.indexOf(this.searchText.toLocaleLowerCase()) > -1 : false)
    );
  }

}
