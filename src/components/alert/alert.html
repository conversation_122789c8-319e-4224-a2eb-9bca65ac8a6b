
  <ion-card text-wrap ion-item detail-push (tap)="itemTapped(alert)" [ngClass]="alert.alertState | AlertStateDisplay: [alert.alertState]">
        <ion-card-content>
          <ion-card-title>
            {{alert.alertDescription}}
          </ion-card-title>
          <p>Control Serial: {{ alert.controlSerialNumber }}</p>
          <p>Control Name: {{ alert.controlName }}</p>
          <p *ngIf="alert.entityName">Component: {{alert.entityName }}</p>
          <p *ngIf="alert.roomName">Room: {{alert.roomName}}</p>
          <p>Created: {{alert.created}}</p>
        </ion-card-content>
      </ion-card>
    
    