import { Component, Input } from '@angular/core';
import { NavController, NavParams } from 'ionic-angular';
import { Alert } from '../../models/alert-model';
@Component({
  selector: 'alert',
  templateUrl: 'alert.html'
})
export class AlertComponent {

  @Input('alert') alert: Alert;
  // @Input('searchText') searchText: string;
  @Input('state') state: string;

  constructor(public navCtrl: NavController, public params: NavParams) {
  }

  ngOnInit(){
  }

  itemTapped(alert: Alert){
    // this.navCtrl.push(AlertDetails, { alertId: alert.fusionAlertId });
  }

}
