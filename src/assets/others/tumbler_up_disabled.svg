<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="78" viewBox="0 0 40 78">
  <defs>
    <rect id="up-b" width="38" height="38" y="20" rx="19"/>
    <filter id="up-a" width="121.1%" height="123.7%" x="-10.5%" y="-5.3%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" result="shadowMatrixOuter1" values="0 0 0 0 0.219   0 0 0 0 0.23115   0 0 0 0 0.3  0 0 0 1 0"/>
      <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter2"/>
      <feGaussianBlur in="shadowOffsetOuter2" result="shadowBlurOuter2" stdDeviation="1"/>
      <feColorMatrix in="shadowBlurOuter2" result="shadowMatrixOuter2" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.15 0"/>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"/>
        <feMergeNode in="shadowMatrixOuter2"/>
      </feMerge>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <use fill="#000" filter="url(#up-a)" xlink:href="#up-b"/>
    <use fill="#383B4D" xlink:href="#up-b"/>
    <rect width="20" height="20" x="9" y="29" fill="#2F3241" rx="10"/>
    <path fill="#808080" d="M8,11 L29.8000031,11 C29.8000031,11 25.8725845,35.2158941 25.8197279,35.5623018 C25.3067566,38.9241699 22.3977223,41.5 18.9000015,41.5 C15.3900027,41.5 12.4833893,38.9101153 11.9778493,35.545696 C11.926576,35.2044667 8,11 8,11 Z"/>
    <rect width="22" height="20" x="8" fill="#d3d3d3" rx="10"/>
    <rect width="12" height="10.909" x="13" y="4.545" fill="#a9a9a9" rx="5.455"/>
  </g>
</svg>
