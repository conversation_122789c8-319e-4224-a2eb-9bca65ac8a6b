<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="78" viewBox="0 0 40 78">
  <defs>

    <rect id="center-b" width="38" height="38" y="20" rx="19"/>
    <filter id="center-a" width="121.1%" height="123.7%" x="-10.5%" y="-5.3%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" result="shadowMatrixOuter1" values="0 0 0 0 0.219   0 0 0 0 0.23115   0 0 0 0 0.3  0 0 0 1 0"/>
      <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter2"/>
      <feGaussianBlur in="shadowOffsetOuter2" result="shadowBlurOuter2" stdDeviation="1"/>
      <feColorMatrix in="shadowBlurOuter2" result="shadowMatrixOuter2" values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.15 0"/>
      <feMerge>
        <feMergeNode in="shadowMatrixOuter1"/>
        <feMergeNode in="shadowMatrixOuter2"/>
      </feMerge>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd">
    <use fill="#000" filter="url(#center-a)" xlink:href="#center-b"/>
    <use fill="#383B4D" xlink:href="#center-b"/>
    <rect width="20" height="20" x="9" y="29" fill="#2F3241" rx="10"/>
    <path fill="#EB2D2D" d="M10.2699959,31.870002 L31.870002,31.870002 C31.870002,31.870002 28.042582,41.33113 27.9897253,41.5736154 C27.476754,43.926923 24.5677197,45.7300041 21.0699989,45.7300041 C17.5600001,45.7300041 14.6533867,43.9170848 14.1478467,41.5619913 C14.0965734,41.3231308 10.2699959,31.870002 10.2699959,31.870002 Z" transform="rotate(90 21.07 38.8)"/>
    <rect width="22" height="22" x="18" y="28" fill="#FC3535" rx="11" transform="rotate(90 29 39)"/>
    <rect width="12" height="12" x="22.712" y="33.288" fill="#E33030" rx="6"/>
  </g>
</svg>
