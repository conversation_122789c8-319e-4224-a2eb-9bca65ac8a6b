import { NgModule } from "@angular/core";
import { AlarmStateActivePipe, AlarmStateOtherPipe } from "./alarm-status.pipe";
import { OnlineFilter, OfflineFilter, FavoriteFilter, NonFavoriteFilter } from "./control-status.pipe";
import { TempConversion } from "./utility.pipe";
import { SensorDisplayIcon } from "./sensor-icon.pipe";
import { DeviceDisplayIcon } from "./device-icon.pipe";
import { LiveValueDisplayPipe } from "./live-value.pipe";
import { EntityTypePipe } from "./entity-type.pipe";
import { ModeDisplay } from "./mode.pipe";
import { RoomsSearchDisplay } from "./room-search.pipe";
import { AlarmActionDisplay } from "./alarm-action.pipe";
import { CalibrationPipe } from "./calibration-status.pipe";
import { OrgSearch } from "./org-search.pipe";

@NgModule({
    declarations: [
        AlarmStateActivePipe,
        AlarmStateOtherPipe,
        OnlineFilter,
        OfflineFilter,
        TempConversion,
        SensorDisplayIcon,
        DeviceDisplayIcon,
        LiveValueDisplayPipe,
        EntityTypePipe,
        ModeDisplay,
        RoomsSearchDisplay,
        AlarmActionDisplay,
        FavoriteFilter,
        NonFavoriteFilter,
        CalibrationPipe,
        OrgSearch
    ],
    imports: [
    ],
    exports: [
        AlarmStateActivePipe,
        AlarmStateOtherPipe,
        OnlineFilter,
        OfflineFilter,
        TempConversion,
        SensorDisplayIcon,
        DeviceDisplayIcon,
        LiveValueDisplayPipe,
        EntityTypePipe,
        ModeDisplay,
        RoomsSearchDisplay,
        AlarmActionDisplay,
        FavoriteFilter,
        NonFavoriteFilter,
        CalibrationPipe,
        OrgSearch
    ],
    providers: []
})
export class PipesModule {}