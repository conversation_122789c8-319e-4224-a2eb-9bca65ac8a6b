import { PipeTransform, Pipe } from "@angular/core";
import { Mode } from "../models/types/mode";
import { ModeDisplayType } from "../models/types/mode-display-type";
import { DeviceType } from "../models/types/device-type";
import { SensorType } from "../models/types/sensor-type";
import { SiteContextService } from "../services/site-context.service";

@Pipe({
    name: 'RoomsSearchDisplay'
})

export class RoomsSearchDisplay implements PipeTransform {
    constructor(private siteContext: SiteContextService){

    }
    transform(entities: any, searchText: string, modeType: ModeDisplayType): any {
        if (entities == null) return entities;
        
        var filteredEntities = entities.filter(entity => {
            if(entity.deviceOrSensorType == DeviceType.RemoteDevice || entity.deviceOrSensorType == SensorType.RemoteSensor) {
                return false;
            }

            if(!this.siteContext.isSiteSelected(entity.siteId)) return false;
            if (!this.hasSearchText(searchText)){
                return true;
            }

            if (this.hasSearchText(searchText) && entity.entityType == 1) {
                return (
                    (entity.entityName ? entity.entityName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.entitySerialNumber ? entity.entitySerialNumber.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.controlName ? entity.controlName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.roomName ? entity.roomName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.controlSerialNumber ? entity.controlSerialNumber.toString().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                );
            }

            if (this.hasSearchText(searchText) && entity.entityType == 2) {
                return (
                    (entity.entityName ? entity.entityName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.entitySerialNumber ? entity.entitySerialNumber.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.controlName ? entity.controlName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.RoomName ? entity.roomName.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1 : false)
                    || (entity.controlSerialNumber ? entity.controlSerialNumber.toString().indexOf(searchText.toLocaleLowerCase()) > -1 : false)

                );
            }
        });

        if (modeType != null || modeType != undefined) {
            filteredEntities = filteredEntities.filter(entity => {
                if(entity.entityType == 2){
                    return this.shouldShowMode(entity,modeType);
                }
                
            })
        };

        // console.log("searchFilterEntities", filteredEntities);
        return filteredEntities;
    }

    hasSearchText(searchText) {
        return searchText || searchText.trim() != "";
    }

    shouldShowMode(device, modeType): boolean {
        if (modeType == null) return true;
        if (modeType == ModeDisplayType.All) return true;
        if (modeType == ModeDisplayType.ManualOn && device.mode == Mode.Manual && device.liveValueData != 0) return true;
        if (modeType == ModeDisplayType.ManualOff && ((device.mode == Mode.Manual && device.liveValueData == 0) || device.mode == Mode.Stop)) return true;
        if (modeType == ModeDisplayType.Auto && device.mode == Mode.Auto) return true;
        else return false;
    }
}