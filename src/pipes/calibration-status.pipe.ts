import { Pipe, PipeTransform } from '@angular/core';
import { CalibrationStatus } from '../models/types/calibration-status';

@Pipe({ name: 'CalibrationStatus' })
export class CalibrationPipe implements PipeTransform {
    transform(value: CalibrationStatus) {
        switch(value){
            case CalibrationStatus.Calibrated:
                return 'Calibrated';
            case CalibrationStatus.Calibrating:
                return 'Calibrating';
            case CalibrationStatus.NotCalibrated:
                return 'Not Calibrated'
        }
    }
}
