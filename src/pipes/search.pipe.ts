import { Pipe, PipeTransform } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { SearchService } from '../services/search.service';

@Pipe({ name: 'SearchFilter' })
export class SearchFilter implements PipeTransform {

    constructor(private searchService: SearchService){

    }

    transform(data: BehaviorSubject<any[]>, searchTerm: string, searchFields: string[]) {
        return this.searchService.filterObservable(searchTerm, data, searchFields);
    }
}

