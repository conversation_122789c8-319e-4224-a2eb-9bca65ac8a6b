import { Component, Pipe, PipeTransform } from '@angular/core';
import { AlarmAction } from '../models/types/alarm-action';

@Pipe({
    name: 'AlarmActionDisplay'
})

export class AlarmActionDisplay implements PipeTransform {
    transform(value: any, args: any[]): any {

        let alarmState = (<AlarmAction>args[0]);

        switch (alarmState) {
            case AlarmAction.Create:
                return 'Created';
            case AlarmAction.Acknowledge:
                return 'Acknowledged';
            case AlarmAction.Resolve:
                return 'Resolved';
            default:
                return null;
        }
    }
}
