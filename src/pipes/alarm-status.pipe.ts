import { Pipe, PipeTransform } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { ControlAlarm } from '../models/control-alarm-model';
import { AlarmState } from '../models/types/alarm-state';

@Pipe({ name: 'AlarmStateActive' })
export class AlarmStateActivePipe implements PipeTransform {
    transform(value: BehaviorSubject<ControlAlarm[]>, args: any) {
        if (value !== undefined) {
            return value.map(alarm => alarm.filter((a: ControlAlarm) => {
                return a.state == AlarmState.Active ||
                    a.state == AlarmState.Acknowledged;
            }));
        }
    }
}

@Pipe({ name: 'AlarmStateOther' })
export class AlarmStateOtherPipe implements PipeTransform {
    transform(value: BehaviorSubject<ControlAlarm[]>, args: any) {
        if (value !== undefined) {
            return value.map(alarm => alarm.filter((a: ControlAlarm) => {
                return a.state === AlarmState.Resolved;
            }));
        }
    }
}