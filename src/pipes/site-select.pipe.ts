import { Pipe, PipeTransform } from '@angular/core';
import { Observable, BehaviorSubject, Subject } from 'rxjs';
import { SiteContextService } from '../services/site-context.service';

@Pipe({ name: 'SiteSelectFilter' })
export class SiteSelectFilter implements PipeTransform {

    private $siteIds: Subject<number[]>;
    private siteIds: number[];

    constructor(private siteContextService: SiteContextService){
        this.$siteIds = this.siteContextService.siteIdsChanged;
        this.$siteIds.subscribe(ids => {
            this.siteIds = ids;
        })
    }

    transform(data: BehaviorSubject<any[]>) {
        return data.map(items => {
            return items.fill(item => {
                if(this.siteIds.indexOf(item.siteId) > -1) return item;
            });
        });
    }
}