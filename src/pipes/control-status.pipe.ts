import { Pipe, PipeTransform } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { Control } from '../models/controls-model';
import * as moment from 'moment';

@Pipe({ name: 'OnlineFilter' })
export class OnlineFilter implements PipeTransform {
    transform(value: BehaviorSubject<Control[]>, args: any[]) {
        if (value !== undefined) {
            return value.map(control => control.filter((ctrl: Control) => {
                if (moment().diff(moment(ctrl.lastHttpPing), 'minutes') > 5) {
                    if (moment().diff(moment(ctrl.lastMqttPing), 'minutes') > 5) {
                        return false;
                    }
                    
                    return false
                }
                return true;
            }));
        }
    }
}

@Pipe({ name: 'OfflineFilter' })
export class OfflineFilter implements PipeTransform {
    transform(value: BehaviorSubject<Control[]>, args: any[]) {
        if (value !== undefined) {
            return value.map(control => control.filter(ctrl => {
                if (moment().diff(moment(ctrl.lastHttpPing), 'minutes') > 5) {
                    if (moment().diff(moment(ctrl.lastMqttPing), 'minutes') > 5) {
                        return true;
                    }
                    return true;
                }
                return false;
            }));
        }
    }
}

@Pipe({ name: 'FavoriteFilter' })
export class FavoriteFilter implements PipeTransform {
    transform(value: BehaviorSubject<Control[]>, args: any[]) {
        if (value !== undefined) {
            return value.map(control => control.filter(ctrl => {
                return ctrl.isFavorite == true;
            }));
        }
    }
}

@Pipe({ name: 'NonFavoriteFilter' })
export class NonFavoriteFilter implements PipeTransform {
    transform(value: BehaviorSubject<Control[]>, args: any[]) {
        if (value !== undefined) {
            return value.map(control => control.filter(ctrl => {
                return ctrl.isFavorite == false;
            }));
        }
    }
}