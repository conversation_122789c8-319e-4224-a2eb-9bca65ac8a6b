import { Pipe, PipeTransform } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { Organization } from '../models/organization.model';
import * as _ from 'lodash'

@Pipe({ name: 'OrgSearch' })
export class OrgSearch implements PipeTransform {


    transform(data: Observable<Organization>, searchTerm: string) {
        return _.filter(data, (org) => {
            if(org.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) return org;
        })
    }
}

