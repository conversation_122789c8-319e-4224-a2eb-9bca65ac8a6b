import { Component, Pipe, PipeTransform } from '@angular/core';
import { Mode } from '../models/types/mode';

@Pipe({
    name: 'ModeDisplay'
})

export class ModeDisplay implements PipeTransform {
    transform(value: any, args: any[]): any {
        if(!args || value == null) return null;
        let mode = Number(<Mode>args[0]);

        switch (mode) {
            case Mode.Auto:
                return 'Auto';
            case Mode.Manual:
                return 'Manual';
            case Mode.Stop:
                return 'Stop';
            default:
                return null;
        }
    }
}
