import { Pipe, PipeTransform } from "../../node_modules/@angular/core";
import { SensorType } from "../models/types/sensor-type";

@Pipe({ name: 'TempConversion' })
export class TempConversion implements PipeTransform {
    transform(value: number, arg: SensorType) {

        if (value == null || value == undefined)
            return '';

        if (arg == null || arg == undefined)
            return '';

        switch (arg) {
            case SensorType.Temperature:
                return ((value * 9 / 5) + 32).toFixed(1) + '° F';
            case SensorType.WaterMeter:
                return (value * 1).toFixed(1) + 'gph';
            case SensorType.Humidity:
            case SensorType.Oxygen:
            case SensorType.Ammonia:
            case SensorType.CO2:
                return (value * 1).toFixed(1) + '%';
            case SensorType.StaticPressure:
                return (value * 1).toFixed(1) + '"';
            default:
                return '•' +value + '•';
        }

    }
}