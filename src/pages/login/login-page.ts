import { Component } from "@angular/core";
import { NavParams, NavController, IonicPage, LoadingController } from "ionic-angular";
import { AuthService } from "../../services/auth.service";
import { LocalStorageHelper } from "../../providers/storage-helper.provider";
import { ConfigurationService } from "../../services/configuration.service";
import { InAppBrowser } from '@ionic-native/in-app-browser';
import { AddControlPage } from "../add-control/add-control";
import { RegisterUserPage } from "../register-user/register-user";

@IonicPage({
    name: 'login-page',
    segment: 'login-page'
})
@Component({
    templateUrl: 'login-page.html',
    selector: 'page-login'
})
export class LoginPage {
    public username: string = "";
    public password: string = "";
    public pushtoken: string = this.localstorage.getData('pushtoken');

    protected configurations = ConfigurationService;

    constructor(public navCtrl: NavController, public navParams: NavParams, private loadCtrl: LoadingController, private authService: AuthService, 
        private localstorage: LocalStorageHelper, private iab: InAppBrowser, private nav: NavController) { }

    ionViewDidLoad() {
        console.log('ionViewDidLoad');        
    }

    simulateScan() {
        if(localStorage.getItem("auth_token") != null || localStorage.getItem("auth_token") != undefined) { 
            this.nav.push(AddControlPage, {serialNo: "1234"});
        }
        else {
            localStorage.setItem("scanned_serial_no", "1234") ;
            this.nav.push(RegisterUserPage);
        }        
    }

    login() {
        var loader = this.loadCtrl.create({
            content: 'Please Wait, Logging You In',
            spinner: 'bubbles',
        });
        loader.present();
        this.authService.login(this.username.toLowerCase(), this.password)
            .subscribe((data) => {
                loader.dismiss();
                console.debug('Auth Service Return', data);
                this.navCtrl.setRoot('home-page');
            }, err => loader.dismiss());
    }

    launchPasswordReset(url) {
        try{
            this.iab.create(this.configurations.baseUrl + '/account/forgotpassword')
        }
        catch{
            window.open(this.configurations.baseUrl + '/account/forgotpassword', '_system');
        }
    }
}