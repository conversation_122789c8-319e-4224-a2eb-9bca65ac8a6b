<ion-content>
    <img style="margin-top: 50%; display: block; margin-left: 2%; margin-right: auto" src="../../assets/imgs/flightline.png">
    <!-- <button (click)="simulateScan()">Scan</button> -->
    <ion-list style="margin-top: 15%">
        <ion-item no-lines>
            <ion-label fixed>Username</ion-label>
            <ion-input type="text" [(ngModel)]="username"></ion-input>
        </ion-item>
        <ion-item no-lines>
            <ion-label fixed>Password</ion-label>
            <ion-input type="password" [(ngModel)]="password"></ion-input>
        </ion-item>
        <!-- <ion-item>
            <ion-label fixed>Token</ion-label>
            <ion-input type="text" [(ngModel)]="pushtoken"></ion-input>
        </ion-item> -->
        <button ion-button block color="primary" (click)="login()">Login</button>
        <button id="submitLogin" ion-button color="secondary" block (click)='launchPasswordReset("")'>Forgot Password</button>
    </ion-list>
</ion-content>