import { Component } from "@angular/core";
import { IonicPage, Checkbox, Events, NavController } from "ionic-angular";
import { Observable, Subscription } from "rxjs";
import { Organization } from "../../models/organization.model";
import { OrganizationDataProvider } from "../../providers/org-data.provider";
import * as _ from 'lodash';
import { LocalStorageHelper } from "../../providers/storage-helper.provider";
import { DBKeys } from "../../models/dbkeys.static";
import { SiteDataProvider } from "../../providers/site-data.provider";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { AlarmDataProvider } from "../../providers/alarm-data.provider";

@IonicPage({
    name: 'user-settings-page',
    segment: 'user-settings-page'
})
@Component({
    selector: 'user-settings-page',
    templateUrl: 'user-settings.html'
})
export class UserSettingsPage {
    public organizations: Observable<Organization[]>;
    public orgName: string;
    public orgId: number;
    private sub: Subscription;
    public allOrgs: Organization;
    public searchText = "";

    constructor(private orgs: OrganizationDataProvider, private localStorage: LocalStorageHelper, private siteData: SiteDataProvider, private events: Events,
                private controlData: ControlDataProvider, public alarmData: AlarmDataProvider, private navCtrl: NavController) {
        this.organizations = this.orgs.getOrganizationsBinding();
        this.allOrgs = new Organization('All Organizations', -1);
        this.orgs.getOrganizations();
        this.orgId = this.localStorage.getData(DBKeys.SELECTED_ORG_ID);
        this.orgName = this.localStorage.getData(DBKeys.SELECTED_ORG_NAME);
        if(!this.orgId) this.organizations.map(orgs => {
            this.orgId = orgs[0].organizationId;
        });
        this.events.subscribe("OrgCreated", data => {
            console.info("org creation data", data);
        })
        // this.sub = this.organizations.subscribe((orgs) => {
        //     orgs.forEach(org => {
        //         if(org.organizationId == this.orgId){

        //         }
        //     })
        // });
    }
       

    public onSelectionChanged(selection) {
        console.log('Selection Changed >>> ', selection);
        localStorage.setItem(DBKeys.SELECTED_ORG_ID, selection);
        this.siteData.getSites();
    }

    public changeOrg(organization: Organization, event){
        console.log(event)
        if(this.orgId == organization.organizationId) event.preventDefault();
        this.orgId = organization.organizationId;
        this.orgName = organization.name;
        console.log("new org Id", this.orgId, organization.organizationId)
        localStorage.setItem(DBKeys.SELECTED_ORG_ID, organization.organizationId.toString());
        localStorage.setItem(DBKeys.SELECTED_ORG_NAME, organization.name);
        this.siteData.getSites();
        this.events.publish("OrgChange")
        this.controlData.getControls().subscribe();
        this.alarmData.getAlarms().subscribe();
        this.navCtrl.setRoot('home-page')

    }

    isChecked(organization: Organization){
        if(!organization) return 'radio-button-off'
        if(organization.organizationId == this.orgId) return 'checkmark-circle';
        return 'radio-button-off';
    }
}