<ion-header>
    <ion-navbar>
        <button ion-button menuToggle>
            <ion-icon name="menu"></ion-icon>
        </button>
        <ion-title>Home</ion-title>
    </ion-navbar>
</ion-header>

<ion-content>
    <ion-searchbar [(ngModel)]="searchText"></ion-searchbar>
    <ion-item>
        <ion-label>Current:</ion-label>
        <span item-right>{{orgName}}</span>
    </ion-item>
    <ion-list>
        <!-- <ion-item>
            <ion-icon name="" item-start></ion-icon>
            <ion-label>Organization</ion-label>
            <ion-select [(ngModel)]="orgId" (ngModelChange)="onSelectionChanged($event)">
                <ion-option *ngFor="let org of organizations | async" [value]="org.organizationId">{{ org.name }}
                </ion-option>
            </ion-select>
        </ion-item> -->
        <ion-item (click)="changeOrg(allOrgs, $event)">
            <ion-label>{{allOrgs.name}}</ion-label>
            <ion-icon item-left color="primary" [name]="isChecked(allOrgs)"></ion-icon>
        </ion-item>
        <ion-item *ngFor="let org of organizations | async | OrgSearch: searchText" (click)="changeOrg(org, $event)">
            <ion-label>{{org?.name}}</ion-label>
            <ion-icon item-left color="primary" [name]="isChecked(org)"></ion-icon>
            <alarm-count item-right [orgId]='org?.organizationId'></alarm-count>
        </ion-item>
    </ion-list>
</ion-content>