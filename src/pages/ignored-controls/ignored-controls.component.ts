import { NavController,  Keyboard, IonicPage, ModalController, ViewController } from "ionic-angular";
import { Component } from "@angular/core";
import { Control } from "../../models/controls-model";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { SiteContextService } from "../../services/site-context.service";

@IonicPage({
    name: 'ignored-controls',
    segment: 'ignored-controls'
})

@Component({
    selector: 'ignored-controls-page',
    templateUrl: 'ignored-controls.html'
})

export class IgnoredControlsPage {
    public controls: Control[];
    public searchText: string = "";

    constructor(private viewCtrl: ViewController, private keyboard: Keyboard, private modalCtrl: ModalController, private controlData: ControlDataProvider,
        private siteContext: SiteContextService) {
       
    }

    ionViewWillLoad() {
         this.controlData.getControlsBinding().subscribe(data => {
            this.controls = data;
        })
    }

    dismiss(){
        this.viewCtrl.dismiss();
    }

    toggleIgnored(control: Control, event){
        console.log(event.checked)
        if(event.checked){
            this.controlData.addIgnoredControl(control).subscribe();
        }
        else{
            this.controlData.removeIgnoredControl(control).subscribe();
        }
    }

    shouldShow(control: Control) {
        if (!this.siteContext.isSiteSelected(control.siteId)) return false;
        if (!this.searchText || this.searchText.trim() == "") return true;
        if (control.name == null || control.name == undefined) control.name = "";
        return (control.name.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
          control.serialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1);
      }

    public handleKeyUp(event) {
        if (event.keyCode == 13) {
            this.keyboard.close();
        }
    }

    public closeKeyboard() {
        this.keyboard.close();
    }

}