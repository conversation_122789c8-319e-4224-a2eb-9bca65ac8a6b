<ion-header>
    <ion-toolbar>
      <ion-title>Ignored Controls</ion-title>
      <ion-buttons right>
        <button ion-button color="primary" (click)="dismiss()">Close</button>
      </ion-buttons>    
    </ion-toolbar>
  </ion-header>

<ion-content>
    <ion-searchbar [(ngModel)]="searchText" (keyup)="handleKeyUp($event)" (ionClear)="closeKeyboard()"></ion-searchbar>
    <ion-list>
      <div *ngFor='let control of controls'>
        <ion-item *ngIf="shouldShow(control)">
          <div item-content>{{control.siteSiteName}} - {{control.name || control.serialNumber}}</div>
          <ion-checkbox item-right (ionChange)="toggleIgnored(control, $event)" [(ngModel)]="control.isIgnored"></ion-checkbox>
      </ion-item>
      </div>
       
    </ion-list>
</ion-content>