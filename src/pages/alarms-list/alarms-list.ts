import { Component } from '@angular/core';
import { <PERSON>ic<PERSON><PERSON>, NavController, NavParams } from 'ionic-angular';
import { FormControl } from '@angular/forms';
import { ControlAlarm } from '../../models/control-alarm-model';
import { Observable, Subscription } from 'rxjs';
import { AlarmDataProvider } from '../../providers/alarm-data.provider';
import { RoomDataProvider } from '../../providers/room-data.provider';
import { LiveValuesSubscription } from '../../providers/livevalues-subscription.provider';
import { AlarmState } from '../../models/types/alarm-state';
import * as _ from 'lodash';

@IonicPage({
  name: 'alarms-list',
  segment: 'alarms-list'
})
@Component({
  selector: 'page-alarms-list',
  templateUrl: 'alarms-list.html',
})
export class AlarmsListPage {
  public myAlarmsPresent: boolean = true;
  public activeAlarmsPresent: boolean = true;
  public resolvedAlarmsPresent: boolean = true;
  private sub: Subscription;

  public alarms: Observable<ControlAlarm[]>;
  public segmentSelected: any = 'mine';
  public searchText: string = undefined;
  public searchControl: FormControl = new FormControl();
  public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12

  constructor(public navCtrl: NavController, public navParams: NavParams, private alarmData: AlarmDataProvider, 
    private roomData: RoomDataProvider, private liveValues: LiveValuesSubscription) {

  }

  ionViewDidLoad() {
    this.alarms = this.alarmData.getAlarmsBinding();
    this.sub = this.alarms.subscribe(alarms => {
      this.myAlarmsPresent = alarms.filter(a => a.state <= 1).length > 0;
      this.activeAlarmsPresent = alarms.filter(a => a.state <= 1).length > 0;
      this.resolvedAlarmsPresent = alarms.filter(a => a.state === 2).length > 0;
      this.myAlarmsPresent = alarms.length > 0;
      var entityIds = [];
      alarms.forEach(a => {
          entityIds.push(a.entityHardwareId);
      });
      entityIds = _.union(entityIds);
      this.liveValues.requestLiveValuesList(entityIds).subscribe();
    });
  }

  ionViewWillEnter() {
    this.getAlarmData();
  }

  handleRefresh(event: any) {
    this.getAlarmData().subscribe((unused) => {
      window.setTimeout(() => event.complete(), 500)
    }, error => { });

  }

  ionViewDidLeave(){
    this.sub.unsubscribe();
  }

  setFilteredItems() {
    // this.alarms = this.alarms.map(alarmObs => {
    //   return alarmObs.filter(alarm => alarm.controlSerialNumber
    //     .toString()
    //     .toLowerCase()
    //     .indexOf(this.searchTerm.toLowerCase()) > -1);
    // })
  }

  getAlarmData() {
    this.alarmData.getAlarms<ControlAlarm[]>().subscribe();
    

    return this.alarms;
  }

  

}


