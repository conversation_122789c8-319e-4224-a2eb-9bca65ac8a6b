<ion-header>
  <ion-navbar>
    <button ion-button menuToggle>
      <ion-icon name="menu"></ion-icon>
      <span><alarm-count></alarm-count></span>
    </button>
    <ion-title>Alarms List</ion-title>
  </ion-navbar>
</ion-header>


<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" [pullMax]=pullMax [pullMin]=pullMin [snapbackDuration]=500>
    <ion-refresher-content pullingText="- Refresh -" refreshingText="Loading Alarms">
    </ion-refresher-content>
  </ion-refresher>
  <ion-searchbar [(ngModel)]="searchText" [formControl]="searchControl"></ion-searchbar>

  <!-- <div>
    <offline-alerts-list></offline-alerts-list>
  </div> -->
  <div>
    <ion-segment padding [(ngModel)]="segmentSelected">
      <ion-segment-button value="mine">Mine</ion-segment-button>
      <ion-segment-button value="active">Active</ion-segment-button>
      <ion-segment-button value="resolved">Resolved</ion-segment-button>
    </ion-segment>

    <div [ngSwitch]="segmentSelected">
      <div *ngIf="!activeAlarmsPresent">
        <ion-list *ngSwitchCase="'mine'">
          <ion-list-header>
            No Alarms to display...
          </ion-list-header>
        </ion-list>
      </div>
      <div *ngIf="activeAlarmsPresent">
        <ion-list *ngSwitchCase="'mine'">
          <ion-list-header>
            My Alarms
          </ion-list-header>
          <alarm-item-template [searchText]="searchText" [alarms]="alarms" [mine]=true></alarm-item-template>
        </ion-list>
      </div>
      <div *ngIf="!activeAlarmsPresent">
        <ion-list *ngSwitchCase="'active'">
          <ion-list-header>

            No Alarms to display...
          </ion-list-header>
        </ion-list>
      </div>
      <div *ngIf="activeAlarmsPresent">
        <ion-list *ngSwitchCase="'active'">
          <ion-list-header>
            Active Alarms
          </ion-list-header>
          <alarm-item-template [searchText]="searchText" [alarms]="alarms" [mine]=false></alarm-item-template>
        </ion-list>
      </div>
      <div *ngIf="!resolvedAlarmsPresent">
        <ion-list *ngSwitchCase="'resolved'">
          <ion-list-header>

            No Alarms to display...
          </ion-list-header>
        </ion-list>
      </div>
      <div *ngIf="resolvedAlarmsPresent">
        <ion-list *ngSwitchCase="'resolved'">
          <ion-list-header>
            Resolved Alarms
          </ion-list-header>
          <alarm-item-template [searchText]="searchText" [alarms]="alarms" [resolved]="true" [mine]=false></alarm-item-template>
        </ion-list>
      </div>
    </div>
  </div>

</ion-content>