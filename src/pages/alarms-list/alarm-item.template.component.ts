import { Component, Input } from "@angular/core";
import { Observable } from "rxjs";
import { ControlAlarm } from "../../models/control-alarm-model";
import { IonicPage, NavController } from "ionic-angular";
import { AlarmState } from "../../models/types/alarm-state";
import { LiveValueService } from "../../services/live-value.service";
import { SiteContextService } from "../../services/site-context.service";
import { RoomDataProvider } from "../../providers/room-data.provider";
import { SensorDisplayIcon } from "../../pipes/sensor-icon.pipe";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { min } from "rxjs/operators";
import { DBKeys } from "../../models/dbkeys.static";
import { EntityType } from "../../models/types/entity-type";

@Component({
    selector: 'alarm-item-template',
    templateUrl: 'alarm-item.template.component.html'
})
export class AlarmItemTemplate {
    @Input('alarms') alarms: Observable<ControlAlarm[]>;
    @Input('resolved') resolved: boolean = false;
    @Input('searchText') searchText: string;
    @Input('mine') mine: boolean;

    entityType: EntityType;

    protected liveValuesMap: Observable<Map<string, any>>;

    constructor(private navCtrl: NavController, private liveValuesService: LiveValueService, private siteContext: SiteContextService, 
        public roomData: RoomDataProvider, public controlData: ControlDataProvider) {
        this.alarms = Observable.from([]);
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    public alarmStateColor(alarm: ControlAlarm) {
        if(alarm.state != AlarmState.Resolved && alarm.silent) return 'primary';
        switch (alarm.state) {
            case AlarmState.Active:
                return 'alarm0';
            case AlarmState.Acknowledged:
                return 'alarm1';
            case AlarmState.Resolved:
                return 'alarm2';
            default:
                return 'facebook';
        }
    }

    public navigateToDetails(alarm: ControlAlarm) {
        console.log('Navigate to Details', alarm);
        this.navCtrl.push('alarm-actions-page', { alarm });
    }

    shouldShow(alarm: ControlAlarm): boolean {
        if(!this.isMine(alarm)) return false;
        if(!this.siteContext.isSiteSelected(alarm.siteId)) return false;
        if (!this.searchText || this.searchText.trim() == "" || !alarm) return true;
        return (alarm.description.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlSerialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            (alarm.entityName? alarm.entityName : "").toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            (alarm.entityHardwareId? alarm.entityHardwareId : "").indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }

    isMine(alarm: ControlAlarm){
        if(!this.mine) return true;
        var valid = false;
        if(!alarm.alarmGroup) return true;
        if(alarm.alarmGroup.members.length > 10){
            var member = alarm.alarmGroup.members.find(agm => agm.userId == alarm.assignedToId);
            var myUser = alarm.alarmGroup.members.find(agm => agm.userId == localStorage.getItem('user_id'));
            if(!myUser) return false;
            if(myUser.placement < member.placement) valid = true;
        }
        alarm.transactions.forEach(t => {
            if(t.assignedToId == localStorage.getItem(DBKeys.USER_ID)) valid = true;
        });
        return valid;
    }
}