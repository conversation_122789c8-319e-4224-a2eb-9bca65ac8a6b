import { NgModule } from '@angular/core';
import { IonicPageModule } from 'ionic-angular';
import { AlarmsListPage } from './alarms-list';
import { AlarmStateActivePipe, AlarmStateOtherPipe } from '../../pipes/alarm-status.pipe';
import { PipesModule } from '../../pipes/pipes.module';
import { OfflineAlertsList } from '../../components/offline-alerts/offline-alerts-list.component';
import { OfflineAlertDataProvider } from '../../providers/offine-alert-data.provider';
import { AlarmItemTemplate } from './alarm-item.template.component';
import { AlarmCountComponentModule } from '../../components/alarm-count/alarm-count.component.module';

@NgModule({
  declarations: [
    AlarmsListPage,
    AlarmItemTemplate,
    OfflineAlertsList
  ],
  imports: [
    IonicPageModule.forChild(AlarmsListPage),
    PipesModule,
    AlarmCountComponentModule  
  ],
  entryComponents: [
    
  ],
  providers: [
  ]
})
export class AlarmsListPageModule {}
