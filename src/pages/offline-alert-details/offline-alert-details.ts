import { Component, Input } from "@angular/core";
import { NavParams, Events, IonicPage, NavController } from "ionic-angular";
import { AuthService } from "../../services/auth.service";
import * as _ from 'lodash';
import { Subscription } from "rxjs";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { AlertDataProvider } from "../../providers/alert-data-provider";
import { OfflineAlert } from "../../models/offline-alert.model";
import { AlertState } from "../../models/types/alert-state";

@IonicPage({
    name: 'offline-alert-details',
    segment: 'offline-alert-details'
})
@Component({
    selector: 'offline-alert-details-page',
    templateUrl: 'offline-alert-details.html'
})

export class OfflineAlertDetailsPage {
    public offlineAlert: OfflineAlert;
    public offlineAlertId: number;
    private sub;

    constructor(public navParams: NavParams, protected alertData: AlertDataProvider, protected controlData: ControlDataProvider, private nav: NavController, protected authService: AuthService, protected events: Events, private auth: AuthService) {
        this.offlineAlert = this.navParams.get('offlineAlert');
        console.log(this.offlineAlert)
    }

    ionViewDidLoad() {
        console.debug('Offline Alert', this.offlineAlert);
        this.events.subscribe('AlertSwitch', alert => {
            this.offlineAlert = alert;
        })
    }

    ionViewWillLeave() {
        if (this.sub) this.sub.unsubscribe();
    }

    public clearOfflineAlert(offlineAlert: OfflineAlert) {
        this.offlineAlert.isActive = false;
        const alertSerialNumber = offlineAlert.serialNumber;
        const userId = localStorage.getItem('user_id');
        this.alertData.clearOfflineAlert(alertSerialNumber, userId);
        this.events.publish("OfflineAlertCleared", offlineAlert);
        this.nav.pop();
    }
}
