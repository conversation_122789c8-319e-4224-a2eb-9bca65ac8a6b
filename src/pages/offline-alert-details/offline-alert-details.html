<ion-header>
    <ion-navbar>
        <ion-title>Offline Details</ion-title>
    </ion-navbar>

</ion-header>

<ion-content padding>
    <ion-card *ngIf="offlineAlert">
        <ion-navbar style="border-top:1px solid gray; border-top-left-radius:5px; border-top-right-radius:5px;">
            <ion-card-header style="white-space: normal;">
                {{offlineAlert.siteName}} - {{offlineAlert.name || offlineAlert.serialNumber}}
            </ion-card-header>
        </ion-navbar>
        <ion-card-content style="text-align:left">
            <!-- <ion-row>
                <ion-col width-50>
                    Description:
                </ion-col>
                <ion-col width-50>
                    A control has gone OFFLINE
                </ion-col>
            </ion-row> -->
            <ion-row>
                <ion-col width-50>
                    Offline Time:
                </ion-col>
                <ion-col width-50>
                    {{offlineAlert?.offlineTime | date: 'short'}}
                </ion-col>
            </ion-row>
            <ion-row *ngIf="offlineAlert.onlineTime">
                <ion-col width-50>
                    Online Time:
                </ion-col>
                <ion-col width-50>
                    {{offlineAlert?.onlineTime | date: 'short'}}
                </ion-col>
            </ion-row>
        </ion-card-content>
        <footer style="margin-bottom:-5px;" *ngIf="offlineAlert.state == 0">
            <button ion-button style="border-radius:5px" full large (click)="clearOfflineAlert(offlineAlert)">Clear</button>
        </footer>
    </ion-card>

    <ion-card *ngIf="!offlineAlert">
        <ion-card-content style="text-align:left">
            This control is currently online
        </ion-card-content>
    </ion-card>
</ion-content>