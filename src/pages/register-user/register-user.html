<!--
  Generated template for the RegisterUserPage page.

  See http://ionicframework.com/docs/components/#navigation for more info on
  Ionic pages and navigation.
-->

<ion-content>
  <img style="margin-top: 10%; display: block; margin-left: 2%; margin-right: auto"
    src="../../assets/imgs/flightline.png">
    <ion-item no-lines style="text-align: center; color:#03A9F4; margin-top: 5%; font-weight: bold;">
      <ion-label block>NEW USER REGISTRATION</ion-label>
    </ion-item>
    
  <ion-list style="margin-top: 10%">
    <ion-item>
      <ion-label stacked>First Name</ion-label>
      <ion-input type="text" [(ngModel)]="firstName"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label stacked>Last Name</ion-label>
      <ion-input type="text" [(ngModel)]="lastName"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label stacked>Username</ion-label>
      <ion-input type="text" [(ngModel)]="userName"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label stacked>Password</ion-label>
      <ion-input type="password" [(ngModel)]="password"></ion-input>
    </ion-item>
    <ion-item>
      <ion-label stacked>Confirm Password</ion-label>
      <ion-input type="password" [(ngModel)]="confirmPassword"></ion-input>
    </ion-item>
    <ion-item no-lines style="background-color: transparent;">
      <ion-label block><span class="error-message ">{{errorMessage}}</span></ion-label>
    </ion-item>
    <button ion-button block color="primary" (click)="registerUser()" >Register</button>
    <button ion-button color="danger" block (click)='backToLogin()'>Back to Login</button>
  </ion-list>
</ion-content>