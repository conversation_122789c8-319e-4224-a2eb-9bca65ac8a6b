import { Component } from '@angular/core';
import { IonicPage, LoadingController, NavController, NavParams } from 'ionic-angular';
import { User } from '../../models/user-model';
import { UserDataProvider } from '../../providers/user-data.provider';
import { AuthService } from '../../services/auth.service';
import { AddControlPage } from '../add-control/add-control';

/**
 * Generated class for the RegisterUserPage page.
 *
 * See https://ionicframework.com/docs/components/#navigation for more info on
 * Ionic pages and navigation.
 */

@IonicPage()
@Component({
  selector: 'page-register-user',
  templateUrl: 'register-user.html',
})
export class RegisterUserPage {

  public userName: string = "";
  public password: string = "";
  public firstName: string = "";
  public lastName: string = "";
  public confirmPassword: string = "";
  public errorMessage: string = "";
  public newUser: User;
  private waitSpinner: any;

  constructor(private navCtrl: NavController,
    private navParams: NavParams,
    private userProvider: UserDataProvider,    
    private loadingController: LoadingController,
    private authService: AuthService) {
    this.errorMessage = "";
    this.newUser = new User;
    this.userProvider.getPinEndpoint().subscribe(data => {
      this.newUser.pin = data;
      console.info("Pin number", data);
    });
  }

  ionViewDidLoad() {
    console.log('ionViewDidLoad RegisterUserPage');
  }

  registerUser() {
    if (this.pageValidations()) {
      this.showPageLoader();
      this.newUser.firstName = this.firstName;
      this.newUser.lastName = this.lastName;
      this.newUser.userName = this.userName;
      this.newUser.password = this.password;
      this.newUser.confirmPassword = this.password;
      //create user
      this.userProvider.getRegisterEndpoint(this.newUser).subscribe((response: User) => {
        console.info("new user creation result", response);
        //add the newly scanned control
        this.waitSpinner.dismiss();
        //Login as the new user
        this.authService.login(this.newUser.userName, this.newUser.password).subscribe((loginResponse) => {
          this.navCtrl.setRoot('home-page');
          //Nav to Add control page
          this.navCtrl.push(AddControlPage, { serialNo: localStorage.getItem('scanned_serial_no'), isNewUser: true});
        }, err => this.waitSpinner.dismiss());

      }, e => {
        console.info("error occured", e.error[""][0]);
        this.errorMessage = e.error[""][0];
        this.waitSpinner.dismiss();
      });
    }
  }

  backToLogin() {
    this.navCtrl.pop();
  }


  showPageLoader() {
    this.waitSpinner = this.loadingController.create({
      spinner: "crescent",
      content: "Registering user and logging in...",
      showBackdrop: true,
      enableBackdropDismiss: true,
      dismissOnPageChange: false
    });
    setTimeout(() => {
      this.waitSpinner.dismiss();

    }, 20000)
    this.waitSpinner.present();
  }

  pageValidations(): boolean {
    if (this.userName.trim() == '') {
      this.errorMessage = "Please enter a Username";
      return false;
    }
    if (this.firstName.trim() == '') {
      this.errorMessage = "Please enter a First name";
      return false;
    }
    if (this.lastName.trim() == '') {
      this.errorMessage = "Please enter a Last Name";
      return false;
    }
    if (this.password.trim() == '') {
      this.errorMessage = "Please enter a Password";
      return false;
    }
    if (this.confirmPassword.trim() == '') {
      this.errorMessage = "Please enter a value for Confirm Password";
      return false;
    }
    if (this.password.trim() != this.confirmPassword.trim()) {
      this.errorMessage = "Password & Confirm Password values don't match";
      return false;
    }
    if (this.password.trim().length < 8) {
      this.errorMessage = "Password should atleast be 7 characters long";
      return false;
    }
    return true;
  }

}
