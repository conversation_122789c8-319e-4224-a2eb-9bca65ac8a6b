import { Observable } from "rxjs/Observable";
import { NavController, NavParams, Keyboard, Events, IonicPage, ModalController } from "ionic-angular";
import { AuthService } from "../../services/auth.service";
import { Component } from "@angular/core";
import { OfflineAlertDetailsPage } from "../offline-alert-details/offline-alert-details";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { OfflineAlert } from "../../models/offline-alert.model";
import { AlertDataProvider } from "../../providers/alert-data-provider";
import { IgnoredControlsPage } from "../ignored-controls/ignored-controls.component";

@IonicPage({
    name: 'offline-alerts',
    segment: 'offline-alerts'
})
@Component({
    selector: 'offline-alerts-page',
    templateUrl: 'offline-alerts.html'
})
export class OfflineAlertsPage {
    public offlineAlerts: Observable<OfflineAlert[]>;
    public selected: string = "offline"
    public searchInput: string = "";
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12

    constructor(public navCtrl: NavController, public navParams: NavParams, private modalCtrl: ModalController,
        private alertsData: AlertDataProvider, private auth: AuthService,
        private keyboard: Keyboard, protected events: Events, protected controlData: ControlDataProvider) {
    }

    ionViewWillLoad() {
        this.offlineAlerts = this.alertsData.setupOfflineAlertsBinding()
        this.alertsData.getOfflineAlerts(localStorage.getItem('user_id'));
    }

    itemTapped(offlineAlert: OfflineAlert) {
        this.navCtrl.push('offline-alert-details', { offlineAlert: offlineAlert });
    }

    public onSearchInput(event) {
        this.offlineAlerts = this.alertsData.setupOfflineAlertsBinding().map(alerts => {
            return alerts
                        .filter(a => a.name.includes(this.searchInput) 
                            || a.serialNumber.toString().includes(this.searchInput));
        })
    }

    public openIgnoredControlsModal(){
        var modal = this.modalCtrl.create(IgnoredControlsPage).present();
    }

    public handleRefresh(event) {
        this.alertsData.getOfflineAlerts(localStorage.getItem('user_id'));
        this.offlineAlerts.subscribe(() => {
            window.setTimeout(() => event.complete(), 500);
        });
    }

    public handleKeyUp(event) {
        if (event.keyCode == 13) {
            this.keyboard.close();
        }
    }

    public closeKeyboard() {
        this.keyboard.close();
    }

}