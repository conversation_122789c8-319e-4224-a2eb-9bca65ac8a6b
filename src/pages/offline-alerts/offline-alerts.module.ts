import { NgModule } from '@angular/core';
import { IonicPageModule } from 'ionic-angular';
import { PipesModule } from '../../pipes/pipes.module';
import { OfflineAlertsPage } from './offline-alerts';
import { AlarmCountComponentModule } from '../../components/alarm-count/alarm-count.component.module';
import { IgnoredControlsPage } from '../ignored-controls/ignored-controls.component';
import { IgnoredControlsPageModule } from '../ignored-controls/ignored-controls.component.module';

@NgModule({
  declarations: [
    OfflineAlertsPage,
  ],
  imports: [
    PipesModule,
    IonicPageModule.forChild(OfflineAlertsPage),
    IgnoredControlsPageModule,
    AlarmCountComponentModule,
  ],
  exports: [
    OfflineAlertsPage,
  ],
  entryComponents: [
    IgnoredControlsPage
  ],
})
export class OfflineAlertsPageModule {}
