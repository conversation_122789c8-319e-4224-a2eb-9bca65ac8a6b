<ion-header>
    <ion-navbar>
        <button ion-button menuToggle>
            <ion-icon name="menu"></ion-icon>
            <span><alarm-count></alarm-count></span>
        </button>
        <ion-title>Offline Alerts</ion-title>
    </ion-navbar>
</ion-header>

<ion-content>
    <ion-refresher (ionRefresh)="handleRefresh($event)" [pullMax]=pullMax [pullMin]=pullMin [snapbackDuration]=500>
        <ion-refresher-content pullingText="- Refresh -" refreshingText="Loading Offline Alerts">
        </ion-refresher-content>
      </ion-refresher>
    <ion-searchbar [(ngModel)]="searchInput" (ionInput)="onSearchInput($event)" (keyup)="handleKeyUp($event)" (ionClear)="closeKeyboard()"></ion-searchbar>
    <button ion-button full (click)="openIgnoredControlsModal()">Set Ignored Controls</button>
    <div>
        <div *ngIf="offlineAlerts == null || (offlineAlerts | async)?.length == 0">
            <ion-list>
                <ion-item>No Offline Alerts...</ion-item>
            </ion-list>
        </div>
        <div *ngIf="offlineAlerts != null || (offlineAlerts | async)?.length >= 1">
            <ion-list>
                <div *ngFor="let offlineAlert of (offlineAlerts | async)">
                    <ion-card text-wrap ion-item detail-push (tap)="itemTapped(offlineAlert)">
                        <ion-card-content>
                            <ion-card-title>
                                A control has gone OFFLINE.
                            </ion-card-title>
                            <p>Site Name: {{offlineAlert.siteName}}</p>
                            <p>Control Serial: {{ offlineAlert.serialNumber}}</p>
                            <p>Control Name: {{ offlineAlert.name }}</p>
                            <p>Offline Time: {{offlineAlert.offlineTime | date: 'short'}}</p>
                            <p *ngIf="offlineAlert.onlineTime">Online Time: {{offlineAlert?.onlineTime | date: 'short'}}</p>
                        </ion-card-content>
                    </ion-card>
                </div>
            </ion-list>
        </div>
    </div>
</ion-content>