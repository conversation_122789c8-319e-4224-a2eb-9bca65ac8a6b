<ion-header>
  <ion-navbar>
    <button ion-button menuToggle>
      <ion-icon name="menu"></ion-icon>
      <span><alarm-count></alarm-count></span>
    </button>
    <ion-title>Controls List</ion-title>
  </ion-navbar>
</ion-header>

<ion-content>
  <ion-refresher (ionRefresh)="handleRefresh($event)" [pullMax]=pullMax [pullMin]=pullMin [snapbackDuration]=500>
    <ion-refresher-content pullingText="- Refresh -" refreshingText="Loading Controls">
    </ion-refresher-content>
  </ion-refresher>
  <ion-searchbar [(ngModel)]="searchText" [formControl]="searchControl"></ion-searchbar>
  <!-- <div *ngIf="loading" class="spinner-container">
    <ion-spinner></ion-spinner>
  </div> -->
  <div>
    <!-- <button (click)="openAddControls()">Open</button> -->
    <ion-segment padding [(ngModel)]="segmentSelected">
      <ion-segment-button value="all">All</ion-segment-button>
      <ion-segment-button value="online">Online</ion-segment-button>
      <ion-segment-button value="offline">Offline</ion-segment-button>
    </ion-segment>

    <div [ngSwitch]="segmentSelected">
      <ion-list *ngSwitchCase="'all'">
        <ion-list-header style="margin-bottom: 2px" *ngIf="(hasFavorites('all', controls | async))">Favorites
        </ion-list-header>
        <div *ngFor="let control of controls | FavoriteFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>
        </div>
        <br />
        <ion-list-header *ngIf="hasNormal('all', controls | async)">Controls</ion-list-header>
        <div *ngFor="let control of controls | NonFavoriteFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>

        </div>


      </ion-list>
      <ion-list *ngSwitchCase="'online'">
        <ion-list-header style="margin-bottom: 2px" *ngIf="hasFavorites('online', controls | async)">Favorites
        </ion-list-header>
        <div
          *ngFor="let control of controls | FavoriteFilter: (control | async) | OnlineFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>

        </div>
        <br />
        <ion-list-header *ngIf="hasNormal('online', controls | async)">Controls</ion-list-header>
        <div
          *ngFor="let control of controls | NonFavoriteFilter: (control | async) | OnlineFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>

        </div>


      </ion-list>
      <ion-list *ngSwitchCase="'offline'">
        <ion-list-header style="margin-bottom: 2px" *ngIf="hasFavorites('offline', controls | async)">Favorites
        </ion-list-header>
        <div
          *ngFor="let control of controls | FavoriteFilter: (control | async) | OfflineFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>

        </div>
        <br />
        <ion-list-header *ngIf="hasNormal('offline', controls | async)">Controls</ion-list-header>
        <div
          *ngFor="let control of controls | NonFavoriteFilter: (control | async) | OfflineFilter: (control | async) | async">
          <control [control]="control" [searchText]="searchText" (controlClicked)="navigateToDetails(control)" (toggleFavoriteClicked)="toggleFavorite(control)"></control>

        </div>

      </ion-list>
    </div>
  </div>
</ion-content>