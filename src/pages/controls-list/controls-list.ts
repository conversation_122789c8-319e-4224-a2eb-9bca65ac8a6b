import { Component } from "@angular/core";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { Observable } from "rxjs";
import { Control } from "../../models/controls-model";
import { IonicPage, NavController, ItemSliding } from "ionic-angular";
import { FormControl } from "@angular/forms";
import { SearchService } from "../../services/search.service";
import { LiveValuesSubscription } from "../../providers/livevalues-subscription.provider";
import { AlarmDataProvider } from "../../providers/alarm-data.provider";
import { SiteContextService } from "../../services/site-context.service";
import * as moment from 'moment';
import { AddControlPage } from "../add-control/add-control";
import { RegisterUserPage } from "../register-user/register-user";

@IonicPage({
    name: 'controls-list',
    segment: 'controls-list'
})
@Component({
    selector: 'page-controls-list',
    templateUrl: 'controls-list.html'
})
export class ControlsListPage {
    public controls: Observable<Control[]>;
    public searchText: string = '';
    searchControl: FormControl = new FormControl();

    public segmentSelected: any = "all";
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12

    constructor(private controlData: ControlDataProvider, private search: SearchService, private nav: NavController,
        private liveValuesSub: LiveValuesSubscription, public alarmData: AlarmDataProvider, private siteContext: SiteContextService) {
    }

    ionViewDidLoad() {
        // this.searchControl.valueChanges.debounceTime(700)
        //     .subscribe(search => {
        //         this.setFilteredItems();
        //     });
    }

    ionViewDidEnter() {
        this.getControlData();
    }

    getControlData() {
        this.controls = this.controlData.getControlsBinding();
        this.controlData.getControls<Control[]>();
        return this.controls;
    }

    //#region "Code to be Deleted in future"
    openAddControls() {
        console.info("Add controls clicked");
        this.nav.push(AddControlPage, {serialNo: "309"})
    }
    //#endregion

    onSearchInput() {
    }

    setFilteredItems() {
        // this.controls = this.controls.map(controlObs => {
        //     return controlObs.filter(ctrl => ctrl.name.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1
        //         || ctrl.serialNumber.toString().toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1);
        // })

    }

    navigateToDetails(control: Control) {
        this.nav.push('control-details-tabs', { control: control, nav: this.nav });
    }

    handleRefresh(event: any) {
        var sub = this.getControlData().subscribe((unused) => {
            console.log("Control List refresh sub")
            window.setTimeout(() => event.complete(), 500)
            // sub.unsubscribe();
        }, error => { });
    }

    public toggleFavorite(control: Control) {
        console.log("toggling favorite")
        this.controlData.toggleFavoriteControl(control.serialNumber, !control.isFavorite).subscribe(() => {
            // window.setTimeout(() => this.controls = this.getControlData(), 10);
            this.controls = this.controlData.updateControls();
        });
        // this.controls = this.controls.map(controls => {
        //     console.log(controls)
        //     var ctrl = controls.find(c => c.serialNumber == control.serialNumber);
        //     if (ctrl.isFavorite) ctrl.isFavorite = false;
        //     else ctrl.isFavorite = true;
            
        //     return controls;
        // })
        
    }

   

    shouldShow(control: Control) {
        if(!this.siteContext.isSiteSelected(control.siteId)) return false;
        if (!this.searchText || this.searchText.trim() == "") return true;
        if (control.name == null || control.name == undefined) control.name = "";
        return (control.name.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            control.serialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }

    shouldShowBadge(control: Control){
        var ac = this.alarmData.getAlarmCounts(control.serialNumber, null, null);
        if(ac.acknowledgedCount + ac.activeCount == 0) return false;
        return true;
    }

    shouldShowSilentBadge(control: Control){
        var ac = this.alarmData.getAlarmCounts(control.serialNumber, null, null);
        if(ac.silentCount == 0) return false;
        return true;
    }

    hasFavorites(val: string, controls: Control[]){
        if(!controls) return false;
        switch(val){
            case "all":
                return controls.filter(ctrl => ctrl.isFavorite).length > 0;
            case "online":
                return controls.filter(ctrl => ctrl.isFavorite && ctrl.status == 0).length > 0;
            case "offline":
                return controls.filter(ctrl => ctrl.isFavorite && ctrl.status != 0).length > 0;
        }
    }

    hasNormal(val: string, controls: Control[]){
        if(!controls) return false;
        switch(val){
            case "all":
                return controls.filter(ctrl => !ctrl.isFavorite).length > 0;
            case "online":
                return controls.filter(ctrl => !ctrl.isFavorite && ctrl.status == 0).length > 0;
            case "offline":
                return controls.filter(ctrl => !ctrl.isFavorite && ctrl.status != 0).length > 0;
        }
    }

    isFavorite(control){
        if(control.isFavorite) return 'star';
        return 'star-outline';
    }
}