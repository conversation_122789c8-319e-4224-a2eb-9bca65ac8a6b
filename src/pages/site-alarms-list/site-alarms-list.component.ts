import { <PERSON>ic<PERSON><PERSON>, NavParams, NavController, Events } from "ionic-angular";
import { Component } from "@angular/core";
import { Observable, Subscription } from "rxjs";
import { ControlAlarm } from "../../models/control-alarm-model";
import { FormControl } from "@angular/forms";
import { AlarmDataProvider } from "../../providers/alarm-data.provider";
import { AlarmState } from "../../models/types/alarm-state";
import { RoomDataProvider } from "../../providers/room-data.provider";
import { ControlDataProvider } from "../../providers/control-data.provider";
import { LiveValueService } from "../../services/live-value.service";
import { Site } from "../../models/site.model";

@IonicPage({
    name: 'site-alarms-list',
    segment: 'site-alarms-list'
})
@Component({
    selector: 'site-alarms-list',
    templateUrl: 'site-alarms-list.html'
})
export class SiteAlarmsList {
    private site: Site;

    protected alarms: Observable<ControlAlarm[]>;
    protected searchText: string = ''
    protected searchControl: FormControl = new FormControl();
    protected activeAlarmsPresent: boolean = true;
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12
    private sub: Subscription;

    constructor(public navCtrl: NavController, private liveValuesService: LiveValueService, private alarmData: AlarmDataProvider,
        public roomData: RoomDataProvider, public controlData: ControlDataProvider, private events: Events, private navParams: NavParams) {
        this.alarms = Observable.from([]);
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    ionViewWillLoad() {
        this.site = this.navParams.data.site;
        this.alarms = this.alarmData.getAlarmsBinding().map(alarms => {
            var als = alarms.filter(a => a.state <=1 && a.siteId == this.site.siteId);
            return als;
        })
        this.sub = this.alarms.subscribe(alarms => {
            this.activeAlarmsPresent = alarms.filter(a => a.state <=1 && a.siteId == this.site.siteId).length > 0;
        });
        this.alarmData.getAlarms();
    }

    handleRefresh(event) {
        this.alarmData.getAlarms().subscribe(alarms => {
            window.setTimeout(() => event.complete(), 500)
        })
    }

    ionViewWillUnload(){
        this.sub.unsubscribe();
    }
     
    public alarmStateColor(alarm: ControlAlarm) {
        switch (alarm.state) {
            case AlarmState.Active:
                return 'alarm0';
            case AlarmState.Acknowledged:
                return 'alarm1';
            case AlarmState.Resolved:
                return 'alarm2';
            default:
                return 'facebook';
        }
    }

    public navigateToDetails(alarm: ControlAlarm) {
        console.log('Navigate to Details', alarm, this.navCtrl);
        this.navCtrl.push('alarm-actions-page', { alarm: alarm });
        // this.events.publish("AlarmNav", alarm)
    }

    shouldShow(alarm: ControlAlarm): boolean {
        if (!this.searchText || this.searchText.trim() == "") return true;
        return (alarm.description.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlSerialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityId.indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
}