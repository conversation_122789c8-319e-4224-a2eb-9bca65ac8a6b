<ion-header>
  <ion-navbar>
    <button ion-button menuToggle>
      <ion-icon name="menu"></ion-icon>
      <span><alarm-count></alarm-count></span>
    </button>
    <ion-title>Dashboard</ion-title>
  </ion-navbar>
</ion-header>

<ion-content padding no-bounce>

  <ion-card (click)="navToAlarms()">
    <ion-card-header>
      <h2 style="color:gainsboro; font-weight:bold; text-align: center">Alarms</h2>
    </ion-card-header>
    <ion-list>
      <button ion-item>
        <ion-row style="text-align:center">
          <ion-col>
            <ion-icon style="color:red !important" name="notifications" item-start></ion-icon>
            <span style="font-size:2.0em">{{ alarmData.getAlarmCounts(null, null).activeCount }}</span>
            <span item-end style="font-size:.9rem; text-transform: uppercase;display:block">Active</span>
          </ion-col>
          <ion-col>
            <ion-icon style="color: orange !important" name="notifications" item-start></ion-icon>
            <span style="font-size:2.0em;">{{ alarmData.getAlarmCounts(null, null).acknowledgedCount }}</span>
            <span item-end style="font-size:.9rem;text-transform: uppercase;display:block">Acknowledged</span>
          </ion-col>
        </ion-row>
      </button>
    </ion-list>
  </ion-card>
  <ion-card (click)="navToControls()">
    <ion-card-header>
      <h2 style="color:gainsboro; font-weight:bold; text-align: center">Controls</h2>
    </ion-card-header>
    <ion-list>
      <button ion-item>
        <ion-row style="text-align:center; line-height:2.0em">
          <ion-col>
            <ion-icon style="color:red !important;" name="notifications" item-start></ion-icon>
            <span style="font-size:2.0em;">{{ getOfflineControlsCount() | async }}</span>
            <span item-end style="font-size:.9rem; text-transform: uppercase;display:block">Offline</span>
          </ion-col>
          <ion-col>
            <ion-icon style="color: green !important" name="notifications" item-start></ion-icon>
            <span style="font-size:2.0em;">{{ getOnlineControlsCount() | async }}</span>
            <span item-end style="font-size:.9rem; text-transform:uppercase; display:block">Online</span>
          </ion-col>
        </ion-row>
      </button>
    </ion-list>
  </ion-card>
  <ion-card>
    <ion-item>
      Version: 3.1.31
    </ion-item>
  </ion-card>
  <!-- <button ion-button full (click)="signalrConnect()">Connect</button> q
  <button ion-button full (click)="signalrDisconnect()">Disconnect</button> -->
  <!-- <ion-card>
    <ion-card-header>
      <h2 style="color:gainsboro; font-weight:bold; text-align: center">Scoreboard</h2>
    </ion-card-header>
    <ion-list>
      <ion-grid>
        <ion-row style="position:sticky; left: 0; top:0; right:0; z-index: 9999; text-align: center">
          <ion-col col-4>
          </ion-col>
          <ion-col col-2>
            <ion-icon name="thermometer"></ion-icon>
          </ion-col>
          <ion-col col-2>
            <ion-icon name="notifications"></ion-icon>
          </ion-col>
          <ion-col col-2>
            <ion-icon name="calendar"></ion-icon>
          </ion-col>
          <ion-col col-2>
            <ion-icon name="nuclear"></ion-icon>
          </ion-col>
        </ion-row>
        <ion-row style="text-align: center" *ngFor='let room of rooms | async'>
          <ion-col col-4>
            <div>{{room.roomName}}</div>
            <div>
              <span style="font-size:.6em">{{ room.controlName }}</span>
            </div>
          </ion-col>
          <ion-col col-2>
            <div *ngIf="roomsMap.get(room.programId)?.roomTemp">{{roomsMap.get(room.programId)?.roomTemp}}</div>
          </ion-col>
          <ion-col col-2>
            <ion-badge color="alarm1">{{alarmData.getAlarmCounts(null, null, room.programId).acknowledgedCount}}</ion-badge> / <ion-badge color="alarm0">{{alarmData.getAlarmCounts(null, null, room.programId).activeCount}}</ion-badge>
          </ion-col>
          <ion-col col-2>
            {{roomsMap.get(room.programId)?.animalDay}}
          </ion-col>
          <ion-col col-2>
            {{roomsMap.get(room.programId)?.roomCFM}}
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-list>
  </ion-card> -->
</ion-content>