import { NgModule } from '@angular/core';
import { IonicPageModule } from 'ionic-angular';
import { HomePage } from './home';

import { AlarmsListTabs } from '../control-details/alarms-list/alarms-list-tabs.component';
import { PipesModule } from '../../pipes/pipes.module';
import { AlarmsListTabsModule } from '../control-details/alarms-list/alarms-list-tabs.component.module';
import { ControlsListPage } from '../controls-list/controls-list';
import { ControlsListModule } from '../controls-list/controls-list.module';
import { AlarmCountComponentModule } from '../../components/alarm-count/alarm-count.component.module';

@NgModule({
  declarations: [
    HomePage,
  ],
  entryComponents: [
    AlarmsListTabs,
    ControlsListPage,
  ],
  imports: [
    IonicPageModule.forChild(HomePage),
    PipesModule,
    AlarmsListTabsModule,
    ControlsListModule,
    AlarmCountComponentModule
  ],
  exports: [
    HomePage,
  ]
})
export class HomePageModule {}