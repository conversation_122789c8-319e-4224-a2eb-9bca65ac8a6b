import { Component } from '@angular/core';
import { NavParams, IonicPage, NavController } from 'ionic-angular';
import { AlarmDataProvider } from '../../providers/alarm-data.provider';
import { ControlDataProvider } from '../../providers/control-data.provider';
import { Observable, pipe, Subscription } from 'rxjs';
import { defaultIfEmpty } from 'rxjs/operators';
import { ControlAlarm } from '../../models/control-alarm-model';
import { Control } from '../../models/controls-model';
import { AlarmState } from '../../models/types/alarm-state';
import { filter } from '../../../node_modules/rxjs/operator/filter';
import { RoomDataProvider } from '../../providers/room-data.provider';
import { Room } from '../../models/room-model';
import { ExtendedLiveDataProvider } from '../../providers/extended-livedata.provider';
import { SignalRService } from '../../services/signalr.service';
import { AlarmsListTabs } from '../control-details/alarms-list/alarms-list-tabs.component';
import { SiteContextService } from '../../services/site-context.service';

@IonicPage({
  name: 'home-page',
  segment: 'home-page'
})
@Component({
  selector: 'page-home',
  templateUrl: 'home.html'
})
export class HomePage {
  mySelectedIndex: number;

  public alarms: Observable<ControlAlarm[]>;
  public controls: Observable<Control[]>;
  public rooms: Observable<any[]>;
  public alarmCount: Observable<number>;
  public controlCount: number;
  public onlineControls: number;
  public offlineControls: number;

  public activeAlarms: Observable<number>;
  public acknowledgedAlarms: Observable<number>;

  public roomsMap: Map<string, any> = new Map<string, any>();
  private sub: Subscription;


  constructor(navParams: NavParams, public alarmData: AlarmDataProvider,
    private controlData: ControlDataProvider,
    private roomData: RoomDataProvider,
    private extendedLiveData: ExtendedLiveDataProvider,
    private signalr: SignalRService,
    private navCtrl: NavController,
    public siteContext: SiteContextService) {
    this.mySelectedIndex = navParams.data.tabIndex || 0;
  }

  ionViewDidLoad() {
    this.alarms = this.alarmData.getAlarms();
    this.controls = this.controlData.getControls();
    if(this.controls) this.sub = this.controls.subscribe(controls => {
      this.controlCount = controls.length;
      this.onlineControls = controls.filter(control => control.status == 0 && this.siteContext.isSiteSelected(control.siteId)).length;
      this.offlineControls = controls.filter(control => control.status != 0 && this.siteContext.isSiteSelected(control.siteId)).length;
    })
    this.rooms = this.roomData.getRooms();
  }

  getOnlineControlsCount(): Observable<number>{
    if(!this.controls) return Observable.of(0);
    return this.controls.map(ctrls => {
      return ctrls.filter(control => control.status == 0 && this.siteContext.isSiteSelected(control.siteId)).length
    });
  }

  getOfflineControlsCount(): Observable<number>{
    if(!this.controls) return Observable.of(0);
    return this.controls.map(ctrls => {
      return ctrls.filter(control => control.status != 0 && this.siteContext.isSiteSelected(control.siteId)).length
    });
  }

  signalrConnect(){
    this.signalr.connect();
  }

  signalrDisconnect(){
    this.signalr.disconnect();
  }

  ionViewDidEnter() {
    // this.rooms.subscribe(rooms => {
    //   rooms.forEach(room => this.extendedLiveData
    //     .requestExtendedLiveValues(room.controlSerialNumber, room.programId.split('.')[1])
    //     .subscribe(response => console.debug('Http Response', response)))
    // });



    this.signalr.sendExtendedRoomData.subscribe(data => {
      var obj = {
        roomTemp: data.dayTemperatureHistory.history[0].toFixed(1),
        animalDay: data.animalDay,
        roomCFM: data.roomCFM
      }
      if (obj.roomTemp == "32.0") obj.roomTemp = null;
      this.roomsMap.set(data.serialNumber + '.' + data.roomIndex, obj)
    });




  }

  ionViewWillLeave() {
    if (this.sub) this.sub.unsubscribe();
  }

  navToAlarms() {
    this.navCtrl.setRoot('alarms-list')
  }

  navToControls() {
    this.navCtrl.setRoot('controls-list')
  }
}