import { <PERSON>ic<PERSON><PERSON>, <PERSON>v<PERSON><PERSON><PERSON>, NavController, Events, ActionSheetController, ToastController } from "ionic-angular";
import { Component } from "@angular/core";
import { RoomDataProvider } from "../../../providers/room-data.provider";
import { Observable } from "rxjs";
import { Device } from "../../../models/device-model";
import { FormControl } from "@angular/forms";
import { Room } from "../../../models/room-model";
import { LiveValueService } from "../../../services/live-value.service";
import { RemoteControlPage } from "../../remote-control-component/remote-control-page-component";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { DeviceType } from "../../../models/types/device-type";
import { EntityType } from "../../../models/types/entity-type";
import { RemoteSettingCommandType } from "../../../models/types/remote-settings-command-type";
@IonicPage({
    name: 'tabs-device-list-page',
    segment: 'tabs-device-list-page'
})
@Component({
    templateUrl: 'tabs-device-list-page.html'
})
export class TabsDeviceListPage {
    private control: any;
    protected rooms: any;
    protected searchText: string = '';
    protected searchControl: FormControl = new FormControl();
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12
    private nav: NavController;

    constructor(private navParams: NavParams, private roomData: RoomDataProvider, private lvService: LiveValueService, public navCtrl: NavController,
        private events: Events, private actionSheet: ActionSheetController, public alarmData: AlarmDataProvider, private toastCtrl: ToastController) {
            this.control = this.navParams.get('control');;
            this.nav = this.navParams.get('nav');
    }
    ionViewWillLoad() {
        this.rooms = JSON.parse(localStorage.getItem(`devices_${103}`));
        this.roomData.getDevicesForControl(this.control.serialNumber).subscribe((rooms) => {
            this.rooms = rooms;
        });
        this.liveValuesMap = this.lvService.getLiveValuesBinding();
        // this.liveValuesMap.subscribe(liveValues => {
        //     this.rooms.subscribe((rm: any) => {
        //         rm.forEach(room => {
        //             room.devices.forEach(device => {
        //                 if (liveValues.has(device.deviceSerialNumber)) {
        //                     device.liveValueData = liveValues.get(device.deviceSerialNumber);
        //                 }
        //             });
        //         });
        //     });
        // });
    }

    ionViewWillUnload(){
        this.events.unsubscribe('AlarmControlTabs')
    }

    protected getDeviceModeText(mode: string): string {
        switch (mode) {
            case "0": return 'auto';
            case "1": return 'manual';
            case "2": return 'stop';
            default: return '';
        }
    }

    getLVColor(value){
        if(value) return 'white';
        return 'orange'
    }

    navToRoomSettings(controlSerialNumber: number,room: Room) {
        this.actionSheet.create({
            title: "Room Navigation",
            buttons: [
                {
                    text: "Room Settings",
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }     
                        var roomData: any = { entityNumber: controlSerialNumber + "." + room.programId, controlSerialNumber: controlSerialNumber, controlName: this.control.name , deviceName: '', remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Room }
                        console.log('Navigating to room settings', roomData);
                        this.nav.push('remote-settings-page', roomData);
                    }
                }                
                ,{
                    text: "Alarm Settings",
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }     
                        //Navigate to room alarm settings
                        var roomData: any = { entityNumber: controlSerialNumber + "." + room.programId, roomId: controlSerialNumber + "." + room.programId, controlSerialNumber: controlSerialNumber, controlName: this.control.name , deviceName: '', remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Room }
                        console.log('Navigating to room alarm settings', roomData);
                        this.nav.push('remote-settings-page', roomData);
                    }
                }                
            ]
        }).present();       
    }
    navToRemoteControl(device: Device) {
        this.actionSheet.create({
            title: "Device Navigation",
            buttons: [
                {
                    text: 'Alarms',
                    handler: () => {
                        this.nav.push('entity-alarms-page', device);
                    }
                },
                {
                    text: 'Remote Control',
                    handler: () => {
                        console.log("Nav to remote control", device)
                        this.nav.push('remote-control-page', device);
                    }
                },
                {
                    text: 'Device Settings',
                    handler: () => {        
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }                     
                        var deviceData: any = {entityNumber: device.deviceSerialNumber, roomId: device.controlSerialNumber + "." + device.roomProgramId, controlSerialNumber: device.controlSerialNumber, controlName: this.control.name, deviceName: device.deviceName, remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Device }
                        console.log("Nav to device settings", deviceData);
                        this.nav.push('remote-settings-page', deviceData);
                    }
                },                
                {
                    text: 'Alarm Settings',
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }     
                        var deviceData: any = {entityNumber: device.deviceSerialNumber, roomId: device.controlSerialNumber + "." + device.roomProgramId, controlSerialNumber: device.controlSerialNumber, controlName: this.control.name, deviceName: device.deviceName, remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Device }                        
                        console.log("Nav to Device alarm settings", deviceData);
                        this.nav.push('remote-settings-page', deviceData);
                    }
                },                
                {
                    text: 'Cancel',
                    role: 'cancel',
                }
            ]
        }).present();
    }
    handleRefresh(event) {
        this.roomData.getDevicesForControl(this.control.serialNumber).subscribe((rooms) => {
            this.rooms = rooms;
            window.setTimeout(() => event.complete(), 500)
        }, err => {
            window.setTimeout(() => event.complete(), 500)
        });
    }

    expandRoom(room){
        if(room.collapsed) room.collapsed = false;
        else room.collapsed = true;
    }
    
    goBack() {
        this.events.publish("PopToRoot")
    }
    shouldShow(device: Device): boolean {
        if (device.deviceType == DeviceType.RemoteDevice) return false;
        if (!this.searchText || this.searchText.trim() == "") return true;
        return (device.deviceName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            device.deviceSerialNumber.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
    shouldShowBadge(device: Device) {
        var ac = this.alarmData.getAlarmCounts(null, device.deviceSerialNumber, null);
        if (ac.acknowledgedCount + ac.activeCount == 0) return false;
        return true;
    }
    shouldShowSilentBadge(device: Device) {
        var ac = this.alarmData.getAlarmCounts(null, device.deviceSerialNumber, null);
        if (ac.silentCount == 0) return false;
        return true;
    }
}