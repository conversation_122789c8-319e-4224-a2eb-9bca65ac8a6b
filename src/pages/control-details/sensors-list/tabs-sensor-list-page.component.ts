import { Ionic<PERSON>age, <PERSON>v<PERSON><PERSON><PERSON>, Nav<PERSON>ontroller, ModalController, Events, ActionSheetController, ToastController } from "ionic-angular";
import { Component } from "@angular/core";
import { RoomDataProvider } from "../../../providers/room-data.provider";
import { Observable } from "rxjs";
import { Sensor } from "../../../models/sensor-model";
import { FormControl } from "@angular/forms";
import { LiveValueService } from "../../../services/live-value.service";
import { LiveValuesSubscription } from "../../../providers/livevalues-subscription.provider";
import { Room } from "../../../models/room-model";
import { EntityGraphsPage } from "../entity-graphs/entity-graphs-page.component";
import { SensorType } from "../../../models/types/sensor-type";
import { ControlsListPage } from "../../controls-list/controls-list";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { EntityType } from "../../../models/types/entity-type";
import { RemoteSettingCommandType } from "../../../models/types/remote-settings-command-type";
@IonicPage({
    name: 'tabs-sensor-list-page',
    segment: 'tabs-sensor-list-page'
})
@Component({
    templateUrl: 'tabs-sensor-list-page.html'
})
export class TabsSensorsListPage {
    public control: any;
    protected rooms: any;
    protected searchText: string = ''
    protected searchControl: FormControl = new FormControl();
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7;
    public pullMin = window.innerHeight * .12;
    private nav: NavController;

    constructor(private navParams: NavParams, public navCtrl: NavController, private roomData: RoomDataProvider, private liveValuesService: LiveValueService, private liveValueSub: LiveValuesSubscription,
    private lvSub: LiveValuesSubscription, private modalCtrl: ModalController, private toastCtrl: ToastController, private events: Events, private actionSheet: ActionSheetController, public alarmData: AlarmDataProvider) {
        this.control = this.navParams.get('control');;
        this.nav = this.navParams.get('nav');
        console.log(this.control, this.nav)
    }
    ionViewWillLoad() {
        if (!this.control.serialNumber) return;
        this.rooms = JSON.parse(localStorage.getItem(`sensors_${this.control.serialNumber}`));
        this.roomData.getSensorsForControl(this.control.serialNumber)
        .catch(() => {
            return Observable.of(null);
        })
        .subscribe(rooms => {
            this.rooms = rooms;
            rooms.forEach((rm: Room) => {
                this.liveValueSub.requestExtendedLiveValues(this.control.serialNumber, rm.programId).subscribe();
            })
        });
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();

    }

    ionViewWillUnload(){
        this.events.unsubscribe('AlarmControlTabs')
    }

    handleRefresh(event) {
        this.roomData.getSensorsForControl(this.control.serialNumber).subscribe(rooms => {
            console.log('refresh sub')
            this.rooms = rooms;
            window.setTimeout(() => event.complete(), 500)
        }, err => {
            console.log('refresh err')
            window.setTimeout(() => event.complete(), 500)
        });
    }

    goBack() {
        this.events.publish("PopToRoot")
    }

    expandRoom(room){
        if(room.collapsed) room.collapsed = false;
        else room.collapsed = true;
    }

    getLVColor(value){
        if(value) return 'white';
        return 'orange'
    }

    navToRoomSettings(controlSerialNumber: number,room: Room) {
        console.log(room)
        this.actionSheet.create({
            title: "Room Navigation",
            buttons: [
                {
                    text: "Room Settings",
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }     
                        var roomData: any = { entityNumber: controlSerialNumber + "." + room.programId, controlSerialNumber: controlSerialNumber ,controlName: this.control.name , deviceName: "", remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Room }
                        console.log('Navigating to room settings', roomData);
                        this.nav.push('remote-settings-page', roomData);
                    }
                }                
                ,{
                    text: "Alarm Settings",
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }     
                        //Navigate to room alarm settings
                        var roomData: any = { entityNumber: controlSerialNumber + "." + room.programId, roomId: controlSerialNumber + "." + room.programId, controlSerialNumber: controlSerialNumber, controlName: this.control.name , deviceName: "", remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Room }
                        console.log('Navigating to room alarm settings', roomData);
                        this.nav.push('remote-settings-page', roomData);
                    }
                }                
            ]
        }).present();       
    }
    navToGraph(sensor: Sensor) {
        // let modal = this.modalCtrl.create(EntityGraphsPage, {'data': sensor});
        // modal.present();
        console.log(sensor)
        this.actionSheet.create({
            title: "Sensor Navigation",
            buttons: [
                {
                    text: 'Alarms',
                    handler: () => {
                        this.nav.push('entity-alarms-page', sensor);
                    }
                },
                {
                    text: 'Graph',
                    handler: () => {
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request graph as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }
                        this.nav.push('entity-graphs-page', sensor);
                    }
                },                
                // {
                //     text: 'Sensor Settings',
                //     handler: () => {                        
                //         var sensorData: any = {entityNumber: sensor.sensorSerialNumber, roomId: sensor.controlSerialNumber + "." + sensor.roomProgramId, controlSerialNumber: sensor.controlSerialNumber, controlName: this.control.name, deviceName: sensor.sensorName, remoteSettingType: RemoteSettingCommandType.EntitySettings, entityType: EntityType.Sensor }
                //         console.log('Navigating to sensor settings', sensorData);
                //         this.navCtrl.push('remote-settings-page', sensorData);
                //     }
                // },
                {
                    text: 'Alarm Settings',
                    handler: () => {           
                        if(this.control.fusionStatus < 13){
                            this.toastCtrl.create({
                              message: "Can't request settings as control is marked offline",
                              position: 'middle',
                              duration: 5000,
                              showCloseButton: true
                            }).present();
                            return;
                          }             
                        var sensorData: any = {entityNumber: sensor.sensorSerialNumber, roomId: sensor.controlSerialNumber + "." + sensor.roomProgramId, controlSerialNumber: sensor.controlSerialNumber, controlName: this.control.name, deviceName: sensor.sensorName, remoteSettingType: RemoteSettingCommandType.AlarmSettings, entityType: EntityType.Sensor }
                        console.log('Navigating to Sensor alarm settings', sensorData);
                        this.nav.push('remote-settings-page', sensorData);
                    }
                },                
                {
                    text: 'Cancel',
                    role: 'cancel',
                }
            ]
        }).present();
    }
    shouldShow(sensor: Sensor): boolean {
        if (sensor.sensorType == SensorType.RemoteSensor) return false;
        if (!this.searchText || this.searchText.trim() == "") return true;
        return (sensor.sensorName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            sensor.sensorSerialNumber.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
    shouldShowBadge(sensor: Sensor) {
        var ac = this.alarmData.getAlarmCounts(null, sensor.sensorSerialNumber, null);
        if (ac.acknowledgedCount + ac.activeCount == 0) return false;
        return true;
    }
    shouldShowSilentBadge(sensor: Sensor) {
        var ac = this.alarmData.getAlarmCounts(null, sensor.sensorSerialNumber, null);
        if (ac.silentCount == 0) return false;
        return true;
    }
}