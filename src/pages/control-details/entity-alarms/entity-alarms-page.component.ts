import { Ionic<PERSON><PERSON>, Nav<PERSON>ara<PERSON>, NavController, Events } from "ionic-angular";
import { Component } from "@angular/core";
import { Observable } from "rxjs";
import { ControlAlarm } from "../../../models/control-alarm-model";
import { FormControl } from "@angular/forms";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { Control } from "../../../models/controls-model";
import { AlarmState } from "../../../models/types/alarm-state";
import { RoomDataProvider } from "../../../providers/room-data.provider";
import { ControlDataProvider } from "../../../providers/control-data.provider";
import { LiveValueService } from "../../../services/live-value.service";
import { Sensor } from "../../../models/sensor-model";
import { Device } from "../../../models/device-model";

@IonicPage({
    name: 'entity-alarms-page',
    segment: 'entity-alarms-page'
})
@Component({
    selector: 'entity-alarms-page',
    templateUrl: 'entity-alarms-page.html'
})
export class EntityAlarms {
    private entity: Sensor & Device;

    protected alarms: Observable<ControlAlarm[]>;
    protected searchText: string = ''
    protected searchControl: FormControl = new FormControl();
    protected activeAlarmsPresent: boolean = true;
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12

    constructor(public navCtrl: NavController, private liveValuesService: LiveValueService, private alarmData: AlarmDataProvider,
        public roomData: RoomDataProvider, public controlData: ControlDataProvider, private events: Events, private navParams: NavParams) {
        this.alarms = Observable.from([]);
        this.entity = this.navParams.data;
        if(this.entity.entitySerialNumber){
            this.entity = this.roomData.getEntity(this.entity.entitySerialNumber);
        }
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    ionViewWillLoad() {
        
        this.alarms = this.alarmData.getAlarms<ControlAlarm[]>().map(alarms => {
            return alarms.filter(a => a.state != AlarmState.Resolved && a.hardwareId == (this.entity.sensorSerialNumber || this.entity.deviceSerialNumber))
        });
       
        this.alarms.subscribe(alarms => {
            this.activeAlarmsPresent = alarms.filter(a => a.state != AlarmState.Resolved && a.hardwareId == (this.entity.sensorSerialNumber || this.entity.deviceSerialNumber)).length > 0;
        });
    }

    
    ionViewWillEnter(){
        this.events.subscribe('AlarmControlTabs', alarm => {
            this.navCtrl.push('alarm-actions-page', { alarm: alarm, nav: this.navCtrl });
        })
    }

    ionViewWillLeave(){
        this.events.unsubscribe('AlarmControlTabs')
    }

    handleRefresh(event) {
        this.alarms = this.alarmData.getAlarms<ControlAlarm[]>().map(alarms => {
            console.log(alarms, this.entity)
            return alarms.filter(a => a.state != AlarmState.Resolved && a.hardwareId == (this.entity.sensorSerialNumber || this.entity.deviceSerialNumber))
        });
        window.setTimeout(() => event.complete(), 500)
    }
    
     
    public alarmStateColor(alarm: ControlAlarm) {
        switch (alarm.state) {
            case AlarmState.Active:
                return 'alarm0';
            case AlarmState.Acknowledged:
                return 'alarm1';
            case AlarmState.Resolved:
                return 'alarm2';
            default:
                return 'facebook';
        }
    }

    public navigateToDetails(alarm: ControlAlarm) {
        console.log('Navigate to Details', alarm);
        this.navCtrl.push('alarm-actions-page', { alarm: alarm, nav: this.navCtrl });
    }

    goBack(){
        this.events.publish("PopToRoot")
    }

    shouldShow(alarm: ControlAlarm): boolean {
        if (!this.searchText || this.searchText.trim() == "") return true;
        return (alarm.description.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlSerialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityId.indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
}