import { Component } from "@angular/core";
import { NavController, NavParams, IonicPage } from "ionic-angular";
import { Control } from "../../models/controls-model";

@IonicPage({
    name: 'control-details-wrapper',
    segment: 'control-details-wrapper'
})
@Component({
    selector: 'control-details-wrapper',
    templateUrl: 'control-details-wrapper.html'
})
export class ControlDetailsWrapper {
    
    protected tabTitle: string = '';
    protected control: Control;

    constructor(public navCtrl: NavController, public navParams: NavParams){
        console.log('Wrapper', this.navParams);
        this.control = this.navParams.get('control');
    }

    onTabChange(tabTitle: string){
        this.tabTitle = tabTitle;
    }
}