import { <PERSON>ic<PERSON><PERSON>, NavParams, NavController, Events } from "ionic-angular";
import { Component } from "@angular/core";
import { Observable, Subscription } from "rxjs";
import { ControlAlarm } from "../../../models/control-alarm-model";
import { FormControl } from "@angular/forms";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { Control } from "../../../models/controls-model";
import { AlarmState } from "../../../models/types/alarm-state";
import { RoomDataProvider } from "../../../providers/room-data.provider";
import { ControlDataProvider } from "../../../providers/control-data.provider";
import { LiveValueService } from "../../../services/live-value.service";

@IonicPage({
    name: 'alarms-list-tabs',
    segment: 'alarms-list-tabs'
})
@Component({
    selector: 'alarms-list-tabs',
    templateUrl: 'alarms-list-tabs.html'
})
export class AlarmsListTabs {
    private control: Control;

    protected alarms: Observable<ControlAlarm[]>;
    protected searchText: string = ''
    protected searchControl: FormControl = new FormControl();
    protected activeAlarmsPresent: boolean = true;
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12;
    private sub: Subscription;
    private nav: NavController;

    constructor(public navCtrl: NavController, private liveValuesService: LiveValueService, private alarmData: AlarmDataProvider,
        public roomData: RoomDataProvider, public controlData: ControlDataProvider, private events: Events, private navParams: NavParams) {
        this.alarms = Observable.from([]);
        this.control = this.navParams.get('control');;
        this.nav = this.navParams.get('nav');
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    ionViewWillLoad() {
        this.alarms = this.alarmData.getAlarmsBinding().map(alarms => {
            return alarms.filter(a => a.state <=1 && a.controlSerialNumber == this.control.serialNumber)
        })
        this.sub = this.alarms.subscribe(alarms => {
            this.activeAlarmsPresent = alarms.filter(a => a.state <=1 && a.controlSerialNumber == this.control.serialNumber).length > 0;
        });
        this.alarmData.getAlarms().subscribe();
    }
    

    handleRefresh(event) {
        this.alarmData.getAlarms().subscribe(alarms => {
            window.setTimeout(() => event.complete(), 500)
        })
        
        
    }
     
    public alarmStateColor(alarm: ControlAlarm) {
        if(alarm.state != AlarmState.Resolved && alarm.silent) return 'primary';
        switch (alarm.state) {
            case AlarmState.Active:
                return 'alarm0';
            case AlarmState.Acknowledged:
                return 'alarm1';
            case AlarmState.Resolved:
                return 'alarm2';
        }
    }

    public navigateToDetails(alarm: ControlAlarm) {
        this.nav.push('alarm-actions-page', { alarm: alarm });
        // this.events.publish("AlarmNav", alarm)
    }

    ionViewWillUnload(){
        this.sub.unsubscribe();
        this.events.unsubscribe('AlarmControlTabs')

    }

    goBack(){
        this.events.publish("PopToRoot")
    }

    shouldShow(alarm: ControlAlarm): boolean {
        if (!this.searchText || this.searchText.trim() == "") return true;
        return (alarm.description.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.controlSerialNumber.toString().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.roomName.toLocaleLowerCase().indexOf(this.searchText.toLocaleLowerCase()) > -1 ||
            alarm.entityId.indexOf(this.searchText.toLocaleLowerCase()) > -1);
    }
}