import { NgModule } from "@angular/core";
import { IonicPageModule } from "ionic-angular";
import { PipesModule } from "../../../pipes/pipes.module";
import { EntityGraphsPage } from "./entity-graphs-page.component";
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgxChartsModule } from "@swimlane/ngx-charts";
import { AppModule } from "../../../app/app.module";

@NgModule({
    declarations: [
        EntityGraphsPage
    ],
    imports: [
        NgxChartsModule,
        PipesModule,
        IonicPageModule.forChild(EntityGraphsPage)
    ],
    exports: [
        EntityGraphsPage
    ],
    providers: [
    ]
})
export class EntityGraphsPageModule {
 }