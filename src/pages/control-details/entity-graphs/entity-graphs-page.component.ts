import { Component, ViewChild } from '@angular/core';
import { IonicPage, NavParams, NavController, Events } from "ionic-angular";
import { Observable, Subscription } from 'rxjs/Rx';
import * as _ from 'lodash';
import { Sensor } from '../../../models/sensor-model';
import { Device } from '../../../models/device-model';
import { LiveValuesSubscription } from '../../../providers/livevalues-subscription.provider';
import { LiveValueService } from '../../../services/live-value.service';
import { GraphService } from '../../../services/graph.service';
import moment from 'moment';
import { ValueTransformer } from '@angular/compiler/src/util';
import { Control } from '../../../models/controls-model';
import { ControlDataProvider } from '../../../providers/control-data.provider';
import { type } from 'os';
import { RoomDataProvider } from '../../../providers/room-data.provider';

@IonicPage({
    name: 'entity-graphs-page',
    segment: 'entity-graphs-page'
})
@Component({
    selector: 'entity-graphs-page',
    templateUrl: 'entity-graphs-page.html'
})
export class EntityGraphsPage {

    public entity: Sensor & Device;
    public graphData: any;
    protected liveValuesMap: Observable<Map<string, any>>;
    private width: number = window.innerWidth;
    private height: number = window.innerHeight - 300;
    public control: Control;
    public typeData: any;
    view: any[] = [this.width, this.height];
    showXAxis = true;
    showYAxis = true;
    gradient = false;
    showLegend = false;
    showXAxisLabel = false;
    xAxisLabel = 'Time';
    showYAxisLabel = true;
    yAxisLabel = 'Value';
    yScaleMax = 1;
    yScaleMin = 0;
    xAxisTicks = [new Date((moment().subtract(24, 'hours')).toDate()), 
                    new Date((moment().subtract(18, 'hours')).toDate()),
                    new Date((moment().subtract(12, 'hours')).toDate()),
                    new Date((moment().subtract(6, 'hours')).toDate()),
                    new Date(moment().toDate())]

    xAxisTickFormatting = value => value.toLocaleTimeString();
    colorScheme = {
        domain: ['#5AA454', '#A10A28', '#C7B42C', '#AAAAAA']
    };

    // line, area
    autoScale = true;

    constructor(private navParams: NavParams, private liveValuesService: LiveValueService, private lvSub: LiveValuesSubscription, private graphService: GraphService, 
        private controlData: ControlDataProvider, private events: Events, private navCtrl: NavController, private roomData: RoomDataProvider) {
        this.entity = this.navParams.data;
        if(this.entity.entitySerialNumber){
            console.log("pulling entity")
            this.entity = this.roomData.getEntity(this.entity.entitySerialNumber);
            console.log("new entity", this.entity)
        }
        this.graphData = [
            {
                "name": this.entity.sensorName,
                "series": []
            }
        ];
        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    xAxisFormat(val: Date){
        return val.toLocaleTimeString();
    }

    ionViewWillLoad() {
        this.control = this.controlData.getControl(this.entity.controlSerialNumber);

        this.typeData = this.graphService.getSensorProperty(this.entity);
        if(!this.typeData) return;
        this.graphService.liveHistoryReceived.subscribe(data => {
            if(data.liveValueHistory.length == 0) return;
            console.log("graphs data", data)
            var values = this.graphService.convertGraphData(this.entity, data, this.control);
            var minMax = this.graphService.getMinMax(data, this.typeData.minRange);
            
            if (this.typeData.property == 'temperature') {
                minMax.min =  Number((minMax.min* 1.8 + 32).toFixed(1));
                minMax.max =  Number((minMax.max* 1.8 + 32).toFixed(1));
            }
            else if (this.typeData.property == 'mass') {
                minMax.min =  Number((minMax.min* 0.00220462).toFixed(0))
                minMax.max =  Number((minMax.max* 0.00220462).toFixed(0))
            }
            else if (this.typeData.property == 'volumeRate') {
                minMax.min =  Number((minMax.min * 0.264172).toFixed(1))
                minMax.max =  Number((minMax.max * 0.264172).toFixed(1))
            }
            else if (this.typeData.property == 'windspeed') {
                minMax.min =  Number((minMax.min * 2.23694).toFixed(2))
                minMax.max =  Number((minMax.max * 2.23694).toFixed(2))
            }
            this.autoScale = false;
            this.yScaleMin = minMax.min;
            this.yScaleMax = minMax.max;
            console.log("Min/Max", minMax)
            this.graphData = [{
                'name': this.entity.sensorName,
                'series': values
            }];

            console.log(this.graphData)
        })
        this.graphService.requestLiveHistory(this.typeData.property, this.entity.sensorSerialNumber);
    }

    
    ionViewWillEnter(){
        this.events.subscribe('AlarmControlTabs', alarm => {
            this.navCtrl.push('alarm-details-tabs', { alarm: alarm, nav: this.navCtrl });
        })
    }

    ionViewWillLeave(){
        this.events.unsubscribe('AlarmControlTabs')
    }
}