<ion-tabs [selectedIndex]="tabSelectedIndex" name="controlDetails">
    <ion-tab [root]="tab1Root" (ionSelect)="onTab('Sensors List')" [rootParams]="{control: control, nav: nav}" tabTitle="Sensors" tabIcon="thermometer" tabUrlPath="Sensors"></ion-tab>
    <ion-tab [root]="tab2Root" (ionSelect)="onTab('Device List')"  [rootParams]="{control: control, nav: nav}" tabTitle="Devices" tabIcon="nuclear" tabUrlPath="Devices"></ion-tab>
    <ion-tab [root]="tab3Root" (ionSelect)="onTab('Alarms List')"  [rootParams]="{control: control, nav: nav}" tabBadgeStyle = "danger" [tabBadge]="alarmData.getAlarmCounts(control.serialNumber, null).activeCount + alarmData.getAlarmCounts(control.serialNumber, null).acknowledgedCount + alarmData.getAlarmCounts(control.serialNumber, null).silentCount" tabTitle="Alarms"  tabIcon="notifications-outline" tabUrlPath="Alarms"></ion-tab>
</ion-tabs>