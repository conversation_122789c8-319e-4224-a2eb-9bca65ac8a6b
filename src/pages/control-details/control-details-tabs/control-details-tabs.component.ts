import { Component, EventEmitter, Output, Input } from '@angular/core';
import { NavParams, IonicPage } from 'ionic-angular';
import { LiveValuesSubscription } from '../../../providers/livevalues-subscription.provider';
import { Subscription, Observable } from 'rxjs';
import { AlarmDataProvider } from '../../../providers/alarm-data.provider';

@IonicPage({
    name: 'control-details-tabs',
    segment: 'control-details-tabs'
})
@Component({
    selector: 'control-details-tabs',
    templateUrl: 'control-details-tabs.html'
})
export class ControlDetailsTabs {
    @Input('control') control: any;

    tabSelectedIndex: number;
    tab1Root: any = 'tabs-sensor-list-page';
    tab2Root: any = 'tabs-device-list-page';
    tab3Root: any = 'alarms-list-tabs';
    private timer: Subscription;
    public nav: any;

    @Output() notify: EventEmitter<string> = new EventEmitter<string>();

    constructor(private navParams: NavParams, private liveValuesSub: LiveValuesSubscription, public alarmData: AlarmDataProvider) {
        this.control = this.navParams.get('control');
        this.nav = this.navParams.get('nav')
        this.tabSelectedIndex = navParams.data.tabIndex || 0;
        this.timer = Observable.timer(0, 10000).subscribe(() => {
            this.liveValuesSub.requestLiveValuesForControl(this.control.serialNumber).subscribe();
        })
    }

    onTab(tabName: string) {
        if (this.control && this.control.serialNumber)
            
            
        this.notify.emit(tabName);
    }
    ngOnDestroy(){
        this.timer.unsubscribe();
    }
}