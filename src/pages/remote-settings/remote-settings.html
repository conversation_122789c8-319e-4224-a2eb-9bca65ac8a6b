<!--*********************************************************************
 * Copyright (c) 2020, Controltech Corporation All Rights Reserved.
 *
 * Description: Remote settings HTML page
*********************************************************************-->
<ion-header>
  <ion-navbar>
    <ion-title *ngIf="entityData.controlName">{{entityData.controlName}}</ion-title>
  </ion-navbar>
</ion-header>

<ion-content class="remote-settings-content">
  <ion-refresher (ionRefresh)="handleRefresh($event)" [pullMax]=pullmax [pullMin]=pullMin [snapbackDuration]=500>
    <ion-refresher-content pullingText="- Refresh -" refreshingText="Refreshing settings...">
    </ion-refresher-content>
  </ion-refresher>
  <div *ngIf="remoteSettings; else emptyControlResponse">
    <ion-item-divider text-wrap sticky *ngIf="remoteSettings.entityName != null || remoteSettings.entityName != ''">
      <button ion-button (click)="expandCollapseAllSettings(remoteSettings.settings)"
        [disabled]="isEmptySettingReceived" class="expand-collapse-all-button">{{expandCollapseBtnTxt}}</button>
      {{remoteSettings.entityName}} {{settingsType}}
    </ion-item-divider>
    <ion-item-group *ngIf="remoteSettings.settings.entitySettings?.length > 0; else alarmSettings">
      <ion-card *ngFor="let setting of remoteSettings.settings.entitySettings" (click)="checkIsSettingEnabled(setting)">
        <ion-card-header *ngIf="setting.header != null || setting.header != ''"
          (click)="toggleExpandCollapseSettings(setting)">
          {{setting.header}}
          <ion-icon style="float: right;" name={{setting.expandCollapseIcon}}></ion-icon>
        </ion-card-header>
        <ion-card-content ngClass={{setting.cssClass}} class="settings-expand-collapse-animation">
          <ion-row *ngFor="let option of setting.options">
            <ion-col *ngIf="option.inputType == remoteSettingsInputType.Number; else normalLabel" col-11>
              <ion-label>{{option.label}} [Min: <span class="min-max-bold">{{option.minValue}}</span>, Max: <span
                  class="min-max-bold">{{option.maxValue}}</span>]</ion-label>
            </ion-col>
            <ng-template #normalLabel>
              <ion-col col-11>
                <ion-label> {{option.label}} </ion-label>
              </ion-col>
            </ng-template>
            <ion-col col-7 *ngIf="option.inputType == remoteSettingsInputType.Number">
              <!-- #region Need to find a way to bring up number keyboard in iOS with dash and dot, so please don't remove the code below --->
              <!-- <ion-input class="ion-textbox" text-center type="text" min={{option.minValue}}
                    max={{option.maxValue}} step={{option.step}} color="light"
                    inputmode="numeric" pattern="[0-9]*" decimal="true" button-char="." decimal-char="."
                    [disabled]="setting.editable&&option.enabled ? false : true" [(ngModel)]="option.value" 
                    (ngModelChange)="convertToNumber(option)"
                    (ionFocus)="saveOldvalue(option.value)"                    
                    (ionBlur)="validateMaxMinRange(setting.settingId,option)"
                    style.border-color={{option.borderColor}}></ion-input> -->
              <!-- #endregion-->
              <ion-item class="ion-textbox-item">
                <ion-input class="ion-textbox" clearInput text-center type="text" min={{option.minValue}}
                  max={{option.maxValue}} step={{option.step}} color="light"
                  [disabled]="setting.editable&&option.enabled ? false : true" [(ngModel)]="option.value"
                  (ionFocus)="saveOldvalue(option.value)" (ionBlur)="validateMaxMinRange(setting,option)"
                  (keyup)="checkIfNumberValid($event, option, setting)" style.border-color={{option.borderColor}}>
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col col-11 *ngIf="option.inputType == remoteSettingsInputType.Text">
              <ion-input class="ion-textbox" text-center [disabled]="setting.editable&&option.enabled ? false : true"
                color="light" [(ngModel)]="option.value" (ionChange)="enableApplyButton()">
              </ion-input>
            </ion-col>
            <ion-item col-4 class="switch-item-ios-overrride"
              *ngIf="option.inputType == remoteSettingsInputType.Switch">
              <ion-toggle color="primary" item-start [disabled]="setting.editable&&option.enabled ? false : true"
                [(ngModel)]="option.value" (ionChange)="enableApplyButton()"></ion-toggle>
            </ion-item>
            <ion-col col-2 *ngIf="option.inputType == remoteSettingsInputType.Switch">
              <ion-label color="primary" class="ion-switch-mode-on" *ngIf="option.value == true">On</ion-label>
              <ion-label color="light" *ngIf="option.value == false">Off</ion-label>
            </ion-col>
            <ion-col col-2 *ngIf="option.inputType == remoteSettingsInputType.Checkbox">
              <ion-checkbox mode="md" class="ion-checkbox" (click)="toggleCheckbox(setting, option)"
                [disabled]="setting.editable&&option.enabled ? false : true" class="ion-checkbox"
                [(ngModel)]="option.value" slot="end"></ion-checkbox>
            </ion-col>
            <ion-col *ngIf="option.inputType == remoteSettingsInputType.Dropbox">
              <ion-select [selectOptions]="customPopoverOptions" placeholder="Please select a value"
                class="ion-drop-down" [disabled]="setting.editable&&option.enabled ? false : true"
                [(ngModel)]="option.value" (ionChange)="enableApplyButton()" col-11>
                <ion-option col-11 *ngFor="let selectorValue of option.valueOptions" [value]="selectorValue.value"
                  checked="false">{{selectorValue.label}}</ion-option>
              </ion-select>
            </ion-col>
            <ion-col *ngIf="option.unit" col-4>
              <ion-label
                *ngIf="option.inputType == remoteSettingsInputType.Number||option.inputType == remoteSettingsInputType.Text||option.inputType == remoteSettingsInputType.Dropbox">
                {{option.unit}}</ion-label>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>
    </ion-item-group>
    <!--*DO NOT REMOVE* - Line breaks to show all list items -->
    <div *ngIf="(!this.btnApplyDisabled ? true : null) && !isKeyboardOpen">
      <br><br><br><br><br>
    </div>
  </div>

  <ng-template #alarmSettings>
    <ion-item-group *ngIf="remoteSettings.settings.alarmSettings?.length > 0; else emptyControlResponse">
      <ion-card *ngFor="let setting of remoteSettings.settings.alarmSettings" (click)="checkIsSettingEnabled(setting)">
        <ion-card-header text-wrap *ngIf="setting.header != null || setting.header != ''"
          (click)="toggleExpandCollapseSettings(setting)">
          <span style="color: red;" *ngIf="alarmIsActive(alarms | async, setting)">{{setting.header}}</span>
          <span style="color: yellow;" *ngIf="alarmIsAcked(alarms | async, setting)">{{setting.header}}</span>
          <span style="color: #0095FE;" *ngIf="alarmIsSilent(alarms| async, setting)">{{setting.header}}</span>
          <span
            *ngIf="!alarmIsActive(alarms | async, setting) && !alarmIsAcked(alarms | async, setting) && !alarmIsSilent(alarms| async, setting)">{{setting.header}}</span>
          <ion-icon style="float: right;" name={{setting.expandCollapseIcon}}></ion-icon>
        </ion-card-header>
        <ion-card-content ngClass={{setting.cssClass}} class="settings-expand-collapse-animation">
          <!-- #region Alarm action buttons alternate alignment method --->
          <!-- <div style="display: flex; justify-content: flex-end">
            <span>
              <button (click)="ackAlarm(setting)" *ngIf="alarmIsActive(alarms | async, setting)" style="width: 9.5em; color: black" color="alarm1" ion-button>Acknowledge</button>
            </span>
            <span>
              <button (click)="resolveAlarm(setting)" *ngIf="alarmIsActive(alarms | async, setting) || alarmIsAcked(alarms | async, setting)" style="width: 10em;" color="alarm2" ion-button>Resolve</button>
            </span>
          </div> -->
          <!-- #endregion-->
          <ion-grid class="alarm-actions-grid"
            *ngIf="alarmIsActive(alarms | async, setting) || alarmIsAcked(alarms | async, setting) || alarmIsSilent(alarms| async, setting)">
            <ion-row>
              <ion-col class="alarm-actions-ack-col">
                <button (click)="ackAlarm(setting)"
                  *ngIf="alarmIsActive(alarms | async, setting) && !alarmIsSilent(alarms| async, setting)"
                  class="alarm-actions-ack-btn" color="alarm1" ion-button>Acknowledge</button>
              </ion-col>
              <ion-col class="alarm-actions-resolve-col">
                <button (click)="resolveAlarm(setting)"
                  *ngIf="alarmIsActive(alarms | async, setting) || alarmIsAcked(alarms | async, setting) || alarmIsSilent(alarms| async, setting)"
                  class="alarm-actions-resolve-btn" color="alarm2" ion-button>Resolve</button>
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-row *ngFor="let option of setting.options">
            <ion-col *ngIf="option.inputType == remoteSettingsInputType.Number; else normalLabel" col-11>
              <ion-label>{{option.label}} [Min: <span class="min-max-bold">{{option.minValue}}</span>, Max: <span
                  class="min-max-bold">{{option.maxValue}}</span>]</ion-label>
            </ion-col>
            <ng-template #normalLabel>
              <ion-col col-11>
                <ion-label *ngIf="option.inputType != remoteSettingsInputType.Button"> {{option.label}} </ion-label>
              </ion-col>
            </ng-template>
            <ion-col col-7 *ngIf="option.inputType == remoteSettingsInputType.Number">
              <!-- #region Need to find a way to bring up number keyboard in iOS with dash and dot, so please don't remove the code below --->
              <!-- <ion-input class="ion-textbox" text-center type="text" min={{option.minValue}}
                    max={{option.maxValue}} step={{option.step}} color="light"
                    inputmode="numeric" pattern="[0-9]*" decimal="true" button-char="." decimal-char="."
                    [disabled]="setting.editable&&option.enabled ? false : true" [(ngModel)]="option.value" 
                    (ngModelChange)="convertToNumber(option)"
                    (ionFocus)="saveOldvalue(option.value)"                    
                    (ionBlur)="validateMaxMinRange(setting.settingId,option)"
                    style.border-color={{option.borderColor}}></ion-input> -->
              <!-- #endregion-->
              <ion-item class="ion-textbox-item">
                <ion-input class="ion-textbox" [ngClass]="setting.editable&&option.enabled ? 'ion-textbox-enabled' : 'ion-textbox-disabled'" 
                  clearInput text-center type="text" min={{option.minValue}}
                  max={{option.maxValue}} step={{option.step}} color="danger"
                  [disabled]="setting.editable&&option.enabled ? false : true" [(ngModel)]="option.value"
                  (ionFocus)="saveOldvalue(option.value)" (ionBlur)="validateMaxMinRange(setting,option)"
                  (keyup)="checkIfNumberValid($event, option, setting)" style.border-color={{option.borderColor}}>
                </ion-input>
              </ion-item>
            </ion-col>
            <button color="alarm0" *ngIf="option.inputType == remoteSettingsInputType.Button"
              (click)="manualFire(setting)" style="width: 44%;"
              [attr.disabled]="alarmIsActive(alarms |
                async, setting) || alarmIsAcked(alarms | async, setting) || alarmIsSilent(alarms| async, setting) ? true : null" ion-button>
              <img src="../../assets/others/hand_white.png" style="width: 21px;"> &nbsp; <span> Manual fire</span>
            </button>
            <ion-col col-11 *ngIf="option.inputType == remoteSettingsInputType.Text">
              <ion-input class="ion-textbox" text-center [disabled]="setting.editable&&option.enabled ? false : true"
                color="light" [(ngModel)]="option.value" (ionChange)="enableApplyButton()">
              </ion-input>
            </ion-col>
            <ion-item col-4 class="switch-item-ios-overrride"
              *ngIf="option.inputType == remoteSettingsInputType.Switch">
              <ion-toggle color={{option.borderColor}} item-start [disabled]="setting.editable&&option.enabled ? false : true"
                [(ngModel)]="option.value" (ionChange)="toggleCheckbox(setting, option)"></ion-toggle>
            </ion-item>
            <ion-col col-2 *ngIf="option.inputType == remoteSettingsInputType.Switch">
              <ion-label color={{option.borderColor}} class="ion-switch-mode-on" *ngIf="option.value == true">On</ion-label>
              <ion-label [color]="option.borderColor == 'primary' ? 'light' : 'danger'" *ngIf="option.value == false">Off</ion-label>
            </ion-col>
            <ion-col col-2 *ngIf="option.inputType == remoteSettingsInputType.Checkbox">
              <ion-checkbox mode="md" class="ion-checkbox" (click)="toggleCheckbox(setting, option)"
                [disabled]="setting.editable&&option.enabled ? false : true" class="ion-checkbox"
                [(ngModel)]="option.value" slot="end"></ion-checkbox>
            </ion-col>
            <ion-col *ngIf="option.inputType == remoteSettingsInputType.Dropbox">
              <ion-select [selectOptions]="customPopoverOptions" placeholder="Please select a value"
                class="ion-drop-down" [disabled]="setting.editable&&option.enabled ? false : true"
                [(ngModel)]="option.value" (ionChange)="enableApplyButton()" col-11>
                <ion-option col-11 *ngFor="let selectorValue of option.valueOptions" [value]="selectorValue.value"
                  checked="false">{{selectorValue.label}}</ion-option>
              </ion-select>
            </ion-col>
            <ion-col *ngIf="option.unit" col-4>
              <ion-label
                *ngIf="option.inputType == remoteSettingsInputType.Number||option.inputType == remoteSettingsInputType.Text||option.inputType == remoteSettingsInputType.Dropbox">
                {{option.unit}}</ion-label>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>
    </ion-item-group>
  </ng-template>
</ion-content>

<ng-template #emptyControlResponse>
  <ion-grid class="error-grid">
    <ion-row justify-content-center align-items-center class="error-grid-row">
      <ion-card *ngIf="this.showErrorInfoTemplate" [color]="this.loadHasErrors ? 'danger' : 'primary'">
        <ion-card-header>
          <ion-card-title>{{pageLoadErrorTitle}}</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          {{pageLoadErrorMessage}}
        </ion-card-content>
      </ion-card>
    </ion-row>
  </ion-grid>
</ng-template>

<ion-footer *ngIf="(!this.btnApplyDisabled ? true : null) && !isKeyboardOpen"
  [attr.hidden]="hideApplyResetButtons? true:null">
  <ion-toolbar>
    <ion-row>
      <ion-col>
        <button ion-button large block color="danger" (click)="resetToOriginal()">Reset</button>
      </ion-col>
      <ion-col>
        <button ion-button large block color="primary" (click)="applySettings()">Apply</button>
      </ion-col>
    </ion-row>
  </ion-toolbar>
</ion-footer>