import { Component } from '@angular/core';
import { info } from 'console';
import { IonicPage, NavController, NavParams, LoadingController, Events } from 'ionic-angular';
import { Observable } from 'rxjs';
import { StringBuilder } from 'typescript-string-operations';
import { DBKeys } from '../../models/dbkeys.static';
import { NewCustomer } from '../../models/new-customer.model';
import { Organization } from '../../models/organization.model';
import { Site } from '../../models/site.model';
import { User } from '../../models/user-model';
import { ControlDataProvider } from '../../providers/control-data.provider';
import { OrganizationDataProvider } from '../../providers/org-data.provider';
import { SiteDataProvider } from '../../providers/site-data.provider';
import { UserDataProvider } from '../../providers/user-data.provider';
import { AuthService } from '../../services/auth.service';


/**
 * Generated class for the AddControlPage page.
 *
 * See https://ionicframework.com/docs/components/#navigation for more info on
 * Ionic pages and navigation.
 */

@IonicPage()
@Component({
  selector: 'page-add-control',
  templateUrl: 'add-control.html',
})
export class AddControlPage {
  public serialNumber: number;
  public newCustomer: NewCustomer = new NewCustomer;
  public newSite: Site;
  public messageTitle: string = "";
  public messageBody: string = "";
  public hasErrors: boolean = false;
  public orgId: number;
  public siteId: number;
  private orgName: string;
  private siteName: string;
  public orgsList: Observable<Organization[]>;
  public sitesList: Observable<Site[]>
  public controlAdded: boolean;
  private messageBuilder = new StringBuilder();
  private orgAndSiteName: User;
  private waitSpinner: any;
  private isNewUser: boolean;
  private roles: string[] = [
    "Can Add Site",
    "Can Manage Security Groups",
    "Can Manage Alarm Groups",
    "Can Edit User Details",
    "Can Manage Roles",
    "View Control Alarms",
    "Can Change Others Password",
    "Add Control",
    "Can View My Organization",
    "Can VNC",
    "View Graphs",
    "View Controls",
    "View Site Management",
    "View Security Groups",
    "View Alarm Groups",
    "View User Management",
    "Can View Reports",
    "View Site Maintenance"
  ];

  constructor(public navCtrl: NavController,
    public loadingController: LoadingController,
    public navParams: NavParams,
    public orgDataProvider: OrganizationDataProvider,
    private siteDataProvider: SiteDataProvider,
    public controlDataProvider: ControlDataProvider,
    private authService: AuthService,
    public userProvider: UserDataProvider,
    private events: Events) {
    console.info("serial number received", this.navParams.get("serialNo"));
    console.info("is new user", this.navParams.get("isNewUser"));
    this.serialNumber = this.navParams.get("serialNo");
    this.isNewUser = this.navParams.get("isNewUser");
    this.newCustomer.controlSerialNumbers = [];
    this.newCustomer.controlSerialNumbers.push(this.navParams.get("serialNo"));
    console.info(this.orgAndSiteName);
    // this.showPageLoader();

    //If new user was created, login, add roles, create org/site and add control
    if (this.isNewUser) {
      //add roles to user
      this.userProvider.addRolesToUserEndpoint(this.roles, localStorage.getItem("user_id")).subscribe((roleData) => {
        console.info("Roles added", roleData);
        localStorage.setItem(DBKeys.ROLES, roleData)
        this.addControl();
      })
    }
    //If user already exists then just create org/site and add control
    else {
      this.controlAdded = false;
      this.orgId = -1;
      this.siteId = -1;
      this.orgsList = this.orgDataProvider.getOrganizationsBinding();
      this.sitesList = this.siteDataProvider.getSitesBinding();
    }
  }

  addControl() {
    this.showPageLoader();
    this.controlAdded = true;
    //get serial number
    this.controlDataProvider.getControlBySerialNo(this.serialNumber).subscribe((data: any) => {
      console.info("Control info", data);
      if (data == null) {

        //create new org, site and control
        this.orgDataProvider.addNewCustomer(this.newCustomer).subscribe((data: Organization) => {
          //set new org info in local storage
          //localStorage.setItem("orgs", JSON.stringify(data));
          localStorage.setItem("organization_id", data.organizationId.toString());
          this.messageTitle = "Info";
          this.hasErrors = false;
          this.orgAndSiteName = JSON.parse(localStorage.getItem("user_info"));
          console.info("Org Site name", this.orgAndSiteName);
          this.events.publish("OrgCreated", this.orgAndSiteName.firstName + " " + this.orgAndSiteName.lastName);
          this.messageBuilder.AppendLine("Control added successfully to app & website <br/><br/>");
          this.messageBuilder.AppendLine("<b>Organization:</b> " + this.orgAndSiteName.firstName + " " + this.orgAndSiteName.lastName + "<br/>");
          this.messageBuilder.AppendLine("<b>Site:</b> " + this.orgAndSiteName.firstName + " " + this.orgAndSiteName.lastName + "<br/>");
          this.messageBuilder.AppendLine("<b>Serial Number:</b> " + this.serialNumber + "<br/>");
          this.messageBody = this.messageBuilder.ToString();
          this.waitSpinner.dismiss();
        });
      }
      else {
        if (data.orgName != null) {
          this.hasErrors = true;
          this.messageTitle = "Error"
          this.messageBody = "Add Control failed. Control already added in the database.";
          this.waitSpinner.dismiss();
        }
        else {
          this.hasErrors = true;
          this.messageTitle = "Error"
          this.messageBody = "Add Control failed because control is already in the system and not assigned to any organization or site. Please contact admin with serial number " + this.serialNumber;
          this.waitSpinner.dismiss();
        }

      }
    })
  }

  assignControl() {
    this.showPageLoader();
    this.controlAdded = true;
    //get serial number
    this.controlDataProvider.getControlBySerialNo(this.serialNumber).subscribe((data: any) => {
      console.info("Control info", data);
      if (data == null) {

        //create new org, site and control
        this.orgDataProvider.addNewControl(this.serialNumber, this.orgId, this.siteId).subscribe((data: Organization) => {
          //set new org info in local storage
          //localStorage.setItem("orgs", JSON.stringify(data));
          this.messageTitle = "Info";
          this.hasErrors = false;
          this.orgAndSiteName = JSON.parse(localStorage.getItem("user_info"));
          console.info("Org Site name", this.orgAndSiteName);
          this.events.publish("OrgCreated", this.orgAndSiteName.firstName + " " + this.orgAndSiteName.lastName);
          this.messageBuilder.AppendLine("Control added successfully to app & website <br/><br/>");
          this.messageBuilder.AppendLine("<b>Organization:</b> " + this.orgName+ "<br/>");
          this.messageBuilder.AppendLine("<b>Site:</b> " + this.siteName + "<br/>");
          this.messageBuilder.AppendLine("<b>Serial Number:</b> " + this.serialNumber + "<br/>");
          this.messageBody = this.messageBuilder.ToString();
          this.waitSpinner.dismiss();
        });
      }
      else {
        if (data.orgName != null) {
          this.hasErrors = true;
          this.messageTitle = "Error"
          this.messageBody = "Add Control failed. Control already added in the database.";
          this.waitSpinner.dismiss();
        }
        else {
          this.hasErrors = true;
          this.messageTitle = "Error"
          this.messageBody = "Add Control failed because control is already in the system and not assigned to any organization or site. Please contact admin with serial number " + this.serialNumber;
          this.waitSpinner.dismiss();
        }

      }
    })
  }

  ionViewDidLoad() {
    console.log('ionViewDidLoad AddControlPage');
  }

  public onOrgChange(organization) {
    if(!organization.organizationId) return;
    this.orgId = organization.organizationId;
    this.orgName = organization.name;
    console.log("new org Id", this.orgId, organization.organizationId)
    localStorage.setItem(DBKeys.SELECTED_ORG_ID, organization.organizationId.toString());
    localStorage.setItem(DBKeys.SELECTED_ORG_NAME, organization.name);
    this.siteDataProvider.getSites();
    this.events.publish("OrgChange")
  }

  public onSiteChange(site){
    if(!site.siteId) return;
    this.siteId = site.siteId;
    this.siteName = site.siteName;
  }

  onBackClick() {
    console.info("Back clicked", this.navCtrl);
    if (this.isNewUser) {
      this.navCtrl.setRoot('home-page');
    }
    else {
      this.navCtrl.pop();
    }
  }

  showPageLoader() {
    this.waitSpinner = this.loadingController.create({
      spinner: "crescent",
      content: "Adding control " + this.serialNumber + "...",
      showBackdrop: true,
      enableBackdropDismiss: true,
      dismissOnPageChange: false
    });
    setTimeout(() => {
      this.waitSpinner.dismiss();

    }, 20000)
    this.waitSpinner.present();
  }

  compareOrgs(o1, o2) {
    return o1 && o2 ? o1.organizationId === o2.organizationId : o1 === o2;
  }

  compareSites(s1, s2) {
    return s1 && s2 ? s1.siteId === s2.siteId : s1 === s2;
  }

}
