<!--
  Generated template for the AddControlPage page.

  See http://ionicframework.com/docs/components/#navigation for more info on
  Ionic pages and navigation.
-->
<ion-header>
  <ion-navbar>
    <ion-title>Add Control</ion-title>
  </ion-navbar>
</ion-header>

<ion-content padding>
  <div *ngIf="!controlAdded">
    <ion-select class="add-control-select" (ionChange)="onOrgChange($event)" placeholder="-- Select Organization --">
      <ion-option *ngFor="let org of orgsList | async" [value]="org">{{org.name}}</ion-option>
    </ion-select> 
    <ion-select class="add-control-select" (ionChange)="onSiteChange($event)" placeholder="-- Select Site --">
      <ion-option *ngFor="let site of sitesList | async" [value]="site">{{site.siteName}}</ion-option>
    </ion-select>
    <button ion-button full color="online" (click)="assignControl()" [disabled]="!siteId || !orgId">Add Control</button>
  </div>
  <ion-grid class="error-grid" *ngIf="controlAdded">
    <ion-row justify-content-center align-items-center class="error-grid-row">
      <ion-card [color]="this.hasErrors ? 'danger' : 'primary'">
        <ion-card-header>
          {{messageTitle}}
        </ion-card-header>
        <ion-card-content>
          <span [innerHTML]="messageBody"></span>
        </ion-card-content>
      </ion-card>
      <button ion-button color="danger" large (click)="onBackClick()">Back</button>
    </ion-row>
  </ion-grid>
</ion-content>