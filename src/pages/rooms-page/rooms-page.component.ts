import { Component, ViewChild } from '@angular/core';
import { IonicPage, NavController, NavParams, ModalController, Platform, Keyboard } from 'ionic-angular';
import { RoomSearchType } from '../../models/types/room-search-type';
import { ButtonsDisplayType } from '../../models/types/buttons-display-type';
import { LiveValueDisplay } from '../../models/types/live-value-display';
import { ModeDisplayType } from '../../models/types/mode-display-type';
import { Observable, Subscription } from 'rxjs';
import { AlarmCount } from '../../models/alarm-count.model';
import { EntityType } from '../../models/types/entity-type';
import { Entity } from '../../models/entity.model';
import * as _ from 'lodash';
import { EntitiesDataProvider } from '../../providers/entities-data.provider';
import { LiveValueService } from '../../services/live-value.service';
import { LiveValuesSubscription } from '../../providers/livevalues-subscription.provider';
import { RoomDataProvider } from '../../providers/room-data.provider';
import { SiteContextService } from '../../services/site-context.service';

/**
 * Generated class for the RoomsPageComponentPage page.
 *
 * See https://ionicframework.com/docs/components/#navigation for more info on
 * Ionic pages and navigation.
 */

@IonicPage({
  name: 'rooms-page',
  segment: 'rooms-page'
})
@Component({
  selector: 'rooms-page',
  templateUrl: 'rooms-page.html',
})
export class RoomsPageComponentPage {

  @ViewChild('searchBar') searchBar;
  @ViewChild('completer') completer;
  @ViewChild('azButton') azButton;
  @ViewChild('valButton') valButton;
  @ViewChild('highButton') highButton;
  @ViewChild('lowButton') lowButton;
  @ViewChild('ionList') ionList: any;

  protected searchData = [
    'All Sensors',
    'All Devices',
    'Fan Devices',
    'Heater Devices',
    'Temperature Sensors',
    'Bin Sensors',
    'Fogger Devices',
    'Motor Devices',
    'Humidity Sensors',
    'FanGroup Devices',
    'Curtain Devices',
    'BinSlide Devices',
    'BallDrop Devices',
    'Light Devices',
    'Switch Devices',
    'Pressure Sensors',
    'Oxygen Sensors',
    'Ammonia Sensors',
    'Gas Sensors',
    'Water Sensors',
    'Wind Sensors',
    'Current Sensors'];

  public entities: Observable<Entity[]>;
  public entArray: any | Entity[];
  public searchInput: string = '';
  private roomValueRequestor: Subscription;
  public refreshing: boolean;
  public isiOS: boolean;
  public open: boolean;

  public title: string;
  public searchTerm: string;
  public allEntities: boolean;

  public searchType: RoomSearchType;
  public liveValueDisplay: LiveValueDisplay;
  public buttonsDisplayType: ButtonsDisplayType;
  public modeType: ModeDisplayType;
  public alarmCount: Observable<Map<string, AlarmCount>>;

  constructor(public navCtrl: NavController,
    public navParams: NavParams,
    private modalCtrl: ModalController,
    private platform: Platform,
    private keyboard: Keyboard,
    private entitiesProvider: EntitiesDataProvider,
    private liveValuesService: LiveValueService,
    private liveValuesProvider: LiveValuesSubscription,
    private roomData: RoomDataProvider,
    private siteContext: SiteContextService) {

    this.searchType == RoomSearchType.None;
    this.buttonsDisplayType = ButtonsDisplayType.All;
    this.entArray = [
      {
        entityName: 'test',
        entitySerialNumber: '',
        controlName: 'test control',
        roomName: 'test room',
        controlSerialNumber: 'dropFromList123',
        entityType: -1
      }
    ]
  }

  ngOnInit() {
    this.refreshing = false;
  }

  ionViewDidEnter() {
    this.entArray.splice(0);
  }

  ngOnDestroy() {
    // this.pageAdapter.unsub();
  }

  handleRefresh(event) {

  }

  openDropdown() {
    this.open = true;
    this.completer.open();
  }

  closeDropdown() {
    this.open = false;
    this.completer.close();
  }

  setOpen(event) {
    this.open = this.completer.isOpen();
  }

  protected onSelected(event) {
    if (event == null) return;
    this.modeType = null;
    this.liveValueDisplay = LiveValueDisplay.LiveValues;
    this.refreshing = false;
    var splitItem = event.split(" ");
    this.searchTerm = splitItem[1];
    console.log(splitItem, this.searchTerm)
    var entityType;
    if (splitItem[2] == "Sensors") {
      entityType = EntityType.Sensor;
      this.allEntities = false;
      this.buttonsDisplayType = ButtonsDisplayType.Sensors;
    }
    else if (splitItem[2] == "Devices") {
      entityType = EntityType.Device;
      this.allEntities = false;
      this.buttonsDisplayType = ButtonsDisplayType.Devices;
    }
    else {
      entityType = EntityType.Both;
      this.allEntities = true;
      this.buttonsDisplayType = ButtonsDisplayType.All;
    }
    this.entitiesProvider.getEntities(this.searchTerm, entityType).subscribe((data) => {
      this.entArray = data;
      var group = Object.keys(_.groupBy(data, 'controlSerialNumber'));
      group.forEach(serial => {
        if(!this.roomData.controlsPulled.has(serial)){
          this.roomData.getDevicesForControl(serial)
          .catch(() => {
            return Observable.of(null)
          })
          .subscribe();
          this.roomData.getSensorsForControl(serial)
          .catch(() => {
            return Observable.of(null)
          })
          .subscribe();
        }
      })
      console.log(group);
    });
  }

  refreshLiveValues() {
    this.requestLiveValues();
    this.refreshing = true;
  }

  requestLiveValues() {
    var entityIds = this.entArray.map(entity => {
      return entity.entitySerialNumber;
    });
    this.liveValuesProvider.requestLiveValuesList(entityIds);
  }

  sortAlphabetical() {

    if (this.searchType == RoomSearchType.AlphabeticalDesc) {
      this.entArray = _.sortBy(this.entArray, ['entityName']).reverse();
    }
    else this.entArray = _.sortBy(this.entArray, ['entityName']);
    if (this.searchType == RoomSearchType.AlphabeticalDesc) {
      this.searchType = RoomSearchType.AlphabeticalAsc
    }
    else this.searchType = RoomSearchType.AlphabeticalDesc;
  }

  sortValue() {
    var filterTerm;
    if (this.liveValueDisplay == LiveValueDisplay.LiveValues) filterTerm = 'liveValueData';
    if (this.liveValueDisplay == LiveValueDisplay.Mode) filterTerm = 'mode';
    if (this.searchType == RoomSearchType.ValDesc) this.entArray = _.sortBy(this.entArray, [filterTerm]);
    else this.entArray = _.sortBy(this.entArray, [filterTerm]).reverse();
    for (var idx = this.entArray.length - 1; idx >= 0; idx--) {
      if (this.entArray[idx].liveValueData == null) {
        var tempSens = this.entArray.splice(idx, 1);
        this.entArray.push(tempSens[0]);
      }
    }

    if (this.searchType == RoomSearchType.ValDesc) {
      this.searchType = RoomSearchType.ValAsc;
    }
    else this.searchType = RoomSearchType.ValDesc;
  }

  filterList() {
        if (this.searchType == RoomSearchType.AlphabeticalAsc) {
            this.entArray = _.sortBy(this.entArray, ['entityName']).reverse();
            return;
        }
        if (this.searchType == RoomSearchType.AlphabeticalDesc) {
            this.entArray = _.sortBy(this.entArray, ['entityName']);
            return;
        }
        var filterTerm;
        if (this.liveValueDisplay == LiveValueDisplay.LiveValues) filterTerm = 'liveValueData';
        if (this.liveValueDisplay == LiveValueDisplay.Mode) filterTerm = 'mode';
        if (this.searchType == RoomSearchType.ValDesc) {

            this.entArray = _.sortBy(this.entArray, [filterTerm]).reverse();
            for (var ix = this.entArray.length - 1; ix >= 0; ix--) {
                if (this.entArray[ix].liveValueData == null) {
                    var tempEnt = this.entArray.splice(ix, 1);
                    this.entArray.push(tempEnt[0]);
                }
            }
            return;
        }
        if (this.searchType == RoomSearchType.ValAsc) {
            this.entArray = _.sortBy(this.entArray, [filterTerm])
            for (var idx = this.entArray.length - 1; idx >= 0; idx--) {
                if (this.entArray[idx].liveValueData == null) {
                    var tempSens = this.entArray.splice(idx, 1);
                    this.entArray.push(tempSens[0]);
                }
            }

            return this.entArray;
        }
  }

  displayLiveValues() {
    this.liveValueDisplay = LiveValueDisplay.LiveValues;
    this.modeType = null;
    this.filterList();
  }

  displayMode() {
    this.liveValueDisplay = LiveValueDisplay.Mode;
    if (this.modeType == null) {
      this.modeType = ModeDisplayType.All;
    }
    else if (this.modeType == ModeDisplayType.All) {
      this.modeType = ModeDisplayType.ManualOn;
    }
    else if (this.modeType == ModeDisplayType.ManualOn) {
      this.modeType = ModeDisplayType.ManualOff;
    }
    else if (this.modeType == ModeDisplayType.ManualOff) {
      this.modeType = ModeDisplayType.Auto;
    }
    else if (this.modeType == ModeDisplayType.Auto) {
      this.modeType = ModeDisplayType.All
    }
    this.filterList();
  }

  handleKeyUp(event) {
    if (event.keyCode == 13) {
      this.keyboard.close();
    }
  }

  closeKeyboard() {
    this.keyboard.close();
  }

  shouldShow(entity: Entity){
    return this.siteContext.isSiteSelected(entity.siteId)
  }

}
