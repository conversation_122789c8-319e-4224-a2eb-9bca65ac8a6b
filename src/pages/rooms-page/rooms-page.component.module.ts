import { NgModule } from '@angular/core';
import { IonicPageModule } from 'ionic-angular';
import { RoomsPageComponentPage } from './rooms-page.component';
import { PipesModule } from '../../pipes/pipes.module';
import { EntityComponent } from '../../components/entity/entity.component';
import { SlidingFooterComponent } from '../../components/sliding-footer/sliding-footer';
import { AlarmCountComponentModule } from '../../components/alarm-count/alarm-count.component.module';

@NgModule({
  declarations: [
    EntityComponent,
    SlidingFooterComponent,
    RoomsPageComponentPage,
  ],
  imports: [
    PipesModule,
    IonicPageModule.forChild(RoomsPageComponentPage),
    AlarmCountComponentModule
  ],
})
export class RoomsPageComponentPageModule {}