<ion-header>
  <ion-navbar>
    <button ion-button menuToggle>
      <ion-icon name="menu"></ion-icon>
      <span><alarm-count></alarm-count></span>
    </button>
    <ion-title>Rooms</ion-title>
    <ion-buttons end>
      <!-- <button *ngIf="!refreshing" ion-button block icon-only outline color='inactive' style="width: 2em" (click)='refreshLiveValues()'>
        <ion-icon name='refresh'></ion-icon>
      </button> -->
      <ion-spinner *ngIf="refreshing"></ion-spinner>
    </ion-buttons>
  </ion-navbar>
</ion-header>

<ion-content>
    <ion-select interface="popover" placeholder="Select A Type" style="width: 100%; max-width: 100%;" (ionChange)="onSelected($event)">
      <ion-option *ngFor="let item of searchData" > {{item}}</ion-option>
    </ion-select>
  <ion-searchbar #searchBar *ngIf="searchTerm" [(ngModel)]='searchInput' (keyup)="handleKeyUp($event)" (ionClear)="closeKeyboard()"></ion-searchbar>
 
  <ion-list #ionList [virtualScroll]="entArray | RoomsSearchDisplay : searchInput : modeType" style="width:100% !important"  >
    <div *virtualItem="let entity" style="min-width:100%; max-width: 100%">
      <ion-item style="max-width: 100%"  *ngIf="shouldShow(entity)">
        <entity *ngIf="entity.entityType == 2" [device]="entity" [liveValueDisplay]="liveValueDisplay" [alarmCount]="alarmCount"></entity>
        <entity *ngIf="entity.entityType == 1" [sensor]="entity" [liveValueDisplay]="liveValueDisplay" [alarmCount]="alarmCount"></entity>
        <p *ngIf="entity.entityType == -1">Please select a type from above.</p>
      </ion-item>
    </div>
  </ion-list>
</ion-content>
<ion-footer>
 
  <ion-toolbar row-no-padding class="footer">
   <sliding-footer [searchType]="searchType" [liveValueDisplay]="liveValueDisplay" [modeType]="modeType" [buttonsDisplayType]="buttonsDisplayType" (sortAZ)="sortAlphabetical()" (sortVal)="sortValue()" (showMode)="displayMode()" (showValues)="displayLiveValues()"></sliding-footer>
  </ion-toolbar>
  
  
</ion-footer>
