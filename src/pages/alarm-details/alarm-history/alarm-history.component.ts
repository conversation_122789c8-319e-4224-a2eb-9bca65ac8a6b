import { IonicPage, NavParams, Events, NavController } from "ionic-angular";
import { Component } from "@angular/core";
import { ControlAlarm } from "../../../models/control-alarm-model";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { AlarmState } from "../../../models/types/alarm-state";
import { SignalRService } from "../../../services/signalr.service";
import { Subscription } from "rxjs";

@IonicPage({
    name: 'alarm-history-page',
    segment: 'alarm-history-page'
})
@Component({
    selector: 'alarm-history-page',
    templateUrl: 'alarm-history.component.html'
})
export class AlarmHistory {
    public alarm: ControlAlarm;
    private sub: Subscription;
    public nav: any;

    constructor(private navParams: NavParams, private alarmDataProvider: AlarmDataProvider, private signalr: SignalRService, private events: Events, private navCtrl: NavController) {
        this.alarm = this.navParams.data.alarm;
        this.nav = this.navParams.data.nav
    }

    ionViewDidLoad() {
        this.sub = this.signalr.alarmDataReceivedEvent$.subscribe((alarm: ControlAlarm) => {
            if (alarm.controlAlarmId == this.alarm.controlAlarmId) {
                this.alarm = alarm;
            }
        });

        if (this.alarm) {
            var temp = this.alarmDataProvider.getAlarm(this.alarm.fusionAlarmKey);
            console.log("Temp", temp);
            if (temp) this.alarm = temp;
        }

        this.events.subscribe('AlarmSwitch', alarm => {
            console.log("Alarm switch", alarm)
            if (this.alarm.fusionAlarmKey != alarm.fusionAlarmKey) {
                this.alarm = alarm;
                try {
                    this.navCtrl.parent.select(0);
                }
                catch{
                    console.log("Attempt to switch tabs failed", this.navCtrl)
                }
            }
        })
    }

    ionViewWillUnload() {
        console.log("Unloaded from History")
        this.sub.unsubscribe();
        this.events.unsubscribe('AlarmSwitch')
    }

    popBack() {
        if (this.nav) this.nav.pop();
        else this.events.publish("PopToRoot");
    }
}