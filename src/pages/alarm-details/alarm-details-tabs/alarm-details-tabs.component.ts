import { IonicPage, NavParams, Events, Platform } from "ionic-angular";
import { Component, Input, Output, EventEmitter, ViewChild } from "@angular/core";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { LiveValuesSubscription } from "../../../providers/livevalues-subscription.provider";
import { ControlAlarm } from "../../../models/control-alarm-model";

@IonicPage({
    name: 'alarm-details-tabs',
    segment: 'alarm-details-tabs'
})
@Component({
    selector: 'alarm-details-tabs',
    templateUrl: 'alarm-details-tabs.html'
})
export class AlarmDetailsTabs {
    @Input('alarm') alarm: ControlAlarm;
    @ViewChild('alarmTabs') alarmTabs;

    public nav: any;
    tabSelectedIndex: number;
    tab1: any = 'alarm-actions-page';
    tab2: any =  'alarm-history-page';    // 'tabs-alarm-asignments-page';
    tab3: any = 'alarm-group-members-page';     // 'remote-control-page';

    @Output() notify: EventEmitter<string> = new EventEmitter<string>();

    constructor(navParams: NavParams, private alarmData: AlarmDataProvider, private liveValues: LiveValuesSubscription, private events: Events, private platform: Platform) {
        this.alarm = <ControlAlarm>navParams.get('alarm');
        this.nav = navParams.get('nav');
        this.tabSelectedIndex = navParams.data.tabIndex || 0;
    }

    ionViewDidLoad(){
        this.liveValues.requestLiveValuesList([this.alarm.entityHardwareId]).subscribe();
        this.events.subscribe('AlarmSwitch', alarm => {
            if (this.alarm.fusionAlarmKey != alarm.fusionAlarmKey) {
                this.alarm = alarm;
            }
        })
    }

    ionViewWillUnload(){
        this.events.unsubscribe('AlarmSwitch');
    }

    onTab(tabName: string) {
        this.notify.emit(tabName);
    }

    pushUp(){
        if(this.nav && this.platform.is('ios')) return 'pushUpIos'
        if(this.nav && this.platform.is('android')) return 'pushUpAndroid'
    }
    
}