    <ion-tabs #alarmTabs [selectedIndex]="tabSelectedIndex" name="alarmDetails" [ngClass]="pushUp() ">
        <ion-tab [root]="tab1" (ionSelect)="onTab('Alarm Actions')" [rootParams]="{alarm: alarm, nav: nav}" tabTitle="Actions" tabIcon="thermometer" tabUrlPath="AlarmActions"></ion-tab>
        <ion-tab [root]="tab2" (ionSelect)="onTab('Alarm Assignments')" [rootParams]="{alarm: alarm, nav: nav}" tabTitle="Assignments" tabIcon="thermometer" tabUrlPath="Assignments"></ion-tab>
        <ion-tab [root]="tab3" (ionSelect)="onTab('Users')" [rootParams]="{alarm: alarm, nav: nav}" tabTitle="Users" tabIcon="thermometer" tabUrlPath="Users"></ion-tab>
    </ion-tabs>