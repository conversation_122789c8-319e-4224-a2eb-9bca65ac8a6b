import { <PERSON>ic<PERSON>age, NavController, NavParams } from "ionic-angular";
import { Component } from "@angular/core";
import { ControlAlarm } from "../../models/control-alarm-model";

@IonicPage({
    name: 'alarm-details-wrapper',
    segment: 'alarm-details-wrapper'
})
@Component({
    selector: 'alarm-details-wrapper',
    templateUrl: 'alarm-details-wrapper.html'
})
export class AlarmDetailsWrapper {
    
    protected tabTitle: string = '';
    protected alarm: any;

    constructor(public navCtrl: NavController, public navParams: NavParams){
        console.log('Wrapper', this.navParams)
        this.alarm = this.navParams.get('alarm');
    }

    onTabChange(tabTitle: string){
        this.tabTitle = tabTitle;
    }
}