<ion-header>
    <ion-navbar>
        <button ion-button menuToggle>
          <ion-icon name="menu"></ion-icon>
        </button>
        
        <ion-title>Alarm Actions</ion-title>

        <ion-buttons right>
            <button *ngIf="alarm.entityType == 2" ion-button icon-only (click)="goToRemoteControl()">
                <ion-icon name="custom-device-switch-off"></ion-icon>
            </button>
            <button ion-button icon-only (click)="goToSettings()">
                <ion-icon name="information-circle"></ion-icon>
            </button>
        </ion-buttons>
    </ion-navbar>
</ion-header>

<ion-content>
    <ion-refresher (ionRefresh)="handleRefresh($event)" [pullMax]=pullMax [pullMin]=pullMin [snapbackDuration]=500>
        <ion-refresher-content pullingText="- Refresh -" refreshingText="Loading Alarm">
        </ion-refresher-content>
      </ion-refresher>
    <ion-card>
        <ion-card-header (click)="toggleCard('actions')" style="height: 3.2em">Actions 
            <ion-icon float-right name="arrow-forward" *ngIf="!actionsExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
            <ion-icon float-right name="arrow-down" *ngIf="actionsExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
        </ion-card-header>
        <ion-card-content *ngIf="actionsExpanded">
            <ion-grid>
                <ion-row>
                    <ion-col col-1 style="padding-top:20px; font-size:1.2em;">
                        <ion-icon item-start name="radio-button-on" [color]="alarmColor()"></ion-icon>
                    </ion-col>
                    <ion-col col-11>
                        <span style="font-weight:bold; font-size:1.1em;">
                            {{ alarm?.description }}
                        </span>
                    </ion-col>
                    <!-- <ion-col col-2>
                        <span style="font-weight:bold; font-size:1.1em">
                            
                        </span>
                    </ion-col> -->
                </ion-row>
                <ion-row>
                    <ion-col col-5>
                        Site:
                    </ion-col>
                    <ion-col col-7>
                        {{alarm?.siteSiteName}}
                    </ion-col>
                </ion-row>
                <ion-row>
                    <ion-col col-5>
                        Control:
                    </ion-col>
                    <ion-col col-7>
                        {{alarm?.controlName}}
                    </ion-col>
                </ion-row>
              
                <ion-row>
                    <ion-col col-5>
                        Room:
                    </ion-col>
                    <ion-col col-7>
                        {{ alarm?.roomName}}
                    </ion-col>
                </ion-row>
                <ion-row *ngIf="alarm?.entityName">
                    <ion-col col-5>
                        Component:
                    </ion-col>
                    <ion-col col-7>
                        {{ alarm?.entityName}}
                    </ion-col>
                </ion-row>
                <ion-row>
                    <ion-col col-5>
                        Received:
                    </ion-col>
                    <ion-col col-7>
                        {{ alarm?.created | date: 'medium'}}
                    </ion-col>
                </ion-row>
                <ion-row *ngIf="alarm?.entityHardwareId">
                    <ion-col col-5>
                        Live:
                    </ion-col>
                    <ion-col col-7 *ngIf=alarm?.entityHardwareId>
                        {{ ((liveValuesMap | async )?.get(alarm?.entityHardwareId)?.value | LiveValueDisplay: [roomData.isSensorOrDevice(alarm?.entityHardwareId),
                            roomData.getEntity(alarm?.entityHardwareId)?.sensorType || roomData.getEntity(alarm?.entityHardwareId)?.deviceType, controlData.getControl(alarm?.controlSerialNumber),
                            (liveValuesMap | async )?.get(alarm?.entityHardwareId)?.label]) || '- -' }}
                    </ion-col>
                </ion-row>
                <ion-row *ngIf="alarm?.entityHardwareId && roomData.getEntity(alarm?.entityHardwareId)?.deviceType < 268435456">
                    <ion-col col-5>
                        Amps:
                    </ion-col>
                    <ion-col col-7 *ngIf="alarm?.entityHardwareId">
                        {{  ((liveValuesMap | async)?.get(alarm.entityHardwareId)?.amps | number: '1.0-3') || '- -' }}
                    </ion-col>
                </ion-row>
            </ion-grid>
            <div *ngIf="alarm?.state == 2 && actionsExpanded">
                <br />
                <br />
                <h1 style="text-align: center;">Alarm is Resolved</h1>
            </div>
        </ion-card-content>
        <ion-grid *ngIf="alarm?.state != 2 && !alarm?.silent && actionsExpanded">
            <ion-row>
                <ion-col col-12>
                    <button class="alarm-button" ion-button large block color="primary" (tap)="bumpAlarm()" [disabled]="!canBump()">
                        <div>Bump</div>
                        <div *ngIf="isAtBumpLimit() && alarm?.alarmGroupId && alarm?.state == 0" style="font-size: .7em;">48 hour bump limit reached</div>
                        <div *ngIf="!isAtBumpLimit() && alarm?.alarmGroupId && alarm?.state == 0" style="font-size: .7em;">Next bump at {{getNextBumpTime() | date: 'medium'}}</div>
                    </button>
                </ion-col>
            </ion-row>
        </ion-grid>
        <ion-grid *ngIf="alarm?.state != 2 && !alarm?.silent && actionsExpanded" >
            <ion-row>
                <ion-col col-12>
                    <button class="alarm-button" ion-button large block color="primary" (tap)="acknowledgeAlarm()" [disabled]="!canAcknowledge()">
                        <div>Acknowledge</div>
                        <div style="font-size: .7em;" *ngIf='alarm?.state == 1'>Ack expire at {{getAckExpireTime() | date: 'medium'}}</div>
                    </button>
                </ion-col>
            </ion-row>
        </ion-grid>

        <ion-grid *ngIf="alarm?.state != 2 && actionsExpanded">
            <ion-row>
                <ion-col col-12>
                    <button ion-button large block color="primary" (click)="resolveAlarm()" [disabled]="!canResolve()">Resolve</button>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-card>


    <ion-card>
        <ion-card-header (click)="toggleCard('members')" style="height: 3.2em">Members
            <ion-icon float-right name="arrow-forward" *ngIf="!membersExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
            <ion-icon float-right name="arrow-down" *ngIf="membersExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
        </ion-card-header>
        <ion-card-content *ngIf="membersExpanded">
            <ion-list no-lines>
                <ion-list-header style="height: 3.2em">
                    Users
                </ion-list-header>
                <ion-row *ngFor="let user of alarm.alarmGroup?.members">
                    <ion-col col-1>
                        <ion-icon name="person" *ngIf="alarm.assignedToId == user.userId"></ion-icon>
                        <ion-icon name="radio-button-on" *ngIf="alarm.assignedToId != user.userId"></ion-icon>
                    </ion-col>
                    <ion-col col-11>
                        <div *ngIf="alarm.assignedToId == user.userId">
                            <b>{{ user.userUsername }}</b>
                        </div>
                        <div *ngIf="alarm.assignedToId != user.userId">
                            {{ user.userUsername }}
                        </div>
                    </ion-col>
                    <ion-col>
                        <ion-icon name="alarm" *ngIf="user.isSnoozed"></ion-icon>
                        <ion-icon color="online" name="cloud-done" *ngIf="user.isOnline"></ion-icon>
                    </ion-col>
                </ion-row>
            </ion-list>
        </ion-card-content>
    </ion-card>

    <ion-card>
        <ion-card-header (click)="toggleCard('assignments')" style="height: 3.2em">Assignments
            <ion-icon float-right name="arrow-forward" *ngIf="!assignmentsExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
            <ion-icon float-right name="arrow-down" *ngIf="assignmentsExpanded" style="font-size: 1.7em; margin-top: -.1em !important;"></ion-icon>
        </ion-card-header>
        <ion-card-content style="text-align: left" *ngIf="assignmentsExpanded">
            <ion-list>
                <ul *ngFor="let transaction of alarm.transactions">
                    <li *ngIf="transaction.actionType == 0 && !alarm.alarmGroupId" class="state-colored">
                        Alarm created by Barn Controller at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 0 && alarm.alarmGroupId" class="state-colored">
                        Alarm created by Barn Controller at {{ transaction.created | date:'medium'}} and assigned to {{ transaction.assignedToUsername }}
                    </li>
                    <li *ngIf="transaction.actionType == 1" class="state-colored">
                        Acknowledged by {{ transaction.initiatedByUserName }} at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 2 && !transaction.autoResolved" class="state-colored">
                        Resolved by {{ transaction.initiatedByUserName }} at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 2 && transaction.autoResolved" class="state-colored">
                        Automatically resolved at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 3" class="state-colored">
                        Bumped to {{ transaction.assignedToUsername }} at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 5 && alarm.alarmGroupId" class="state-colored">
                        Acknowledge expired, alarm bumped to {{ transaction.assignedToUsername }} at {{ transaction.created | date:'medium' }}
                    </li>
                    <li *ngIf="transaction.actionType == 5 && (!alarm.alarmGroupId || alarm.alarmGroupId == 0)" class="state-colored">
                        Acknowledge expired, alarm bumped to everyone at {{ transaction.created | date:'medium' }}
                    </li>
                </ul>
            </ion-list>
        </ion-card-content>
    </ion-card>
</ion-content>