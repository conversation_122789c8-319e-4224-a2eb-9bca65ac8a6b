import { IonicPage, NavParams, Events, NavController } from "ionic-angular";
import { Component } from "@angular/core";
import { ControlAlarm } from "../../../models/control-alarm-model";
import { AlarmDataProvider } from "../../../providers/alarm-data.provider";
import { AlarmState } from "../../../models/types/alarm-state";
import { SignalRService } from "../../../services/signalr.service";
import { DBKeys } from "../../../models/dbkeys.static";
import { Subscription, Observable } from "rxjs";
import { RoomDataProvider } from "../../../providers/room-data.provider";
import { LiveValueService } from "../../../services/live-value.service";
import { ControlDataProvider } from "../../../providers/control-data.provider";
import { LiveValuesSubscription } from "../../../providers/livevalues-subscription.provider";
import { threadId } from "worker_threads";
import moment from "moment";
import { RemoteSettingCommandType } from "../../../models/types/remote-settings-command-type";
import { EntityType } from "../../../models/types/entity-type";

@IonicPage({
    name: 'alarm-actions-page',
    segment: 'alarm-actions-page'
})
@Component({
    selector: 'alarm-actions-page',
    templateUrl: 'alarm-actions.component.html'
})
export class AlarmActions {
    public alarm: ControlAlarm;
    private sub: Subscription;
    protected liveValuesMap: Observable<Map<string, any>>;
    public pullMax = window.innerHeight * .7
    public pullMin = window.innerHeight * .12
    public nav: any;
    public actionsExpanded: boolean = true;
    public membersExpanded: boolean;
    public assignmentsExpanded: boolean;

    private ackPressed: boolean;
    private bumpPressed: boolean;
    private resolvePressed: boolean;

    constructor(private navParams: NavParams, private alarmDataProvider: AlarmDataProvider, private signalr: SignalRService, private liveValues: LiveValuesSubscription,
        public roomData: RoomDataProvider, private liveValuesService: LiveValueService, public controlData: ControlDataProvider, private events: Events, public navCtrl: NavController) {
        this.alarm = this.navParams.data;
        if (!this.alarm.state) this.alarm = this.navParams.data.alarm;
        this.nav = this.navParams.data.nav

        this.liveValuesMap = this.liveValuesService.getLiveValuesBinding();
    }

    ionViewDidLoad() {

        window.setTimeout(() => {
            this.liveValues.requestLiveValuesList([this.alarm.entityHardwareId]).subscribe(), 1000
        });
        console.log("Loaded alarm actions", this.alarm, this.navParams)
        if (!this.alarm) return;
        if (!this.alarm.description) this.alarm = this.navParams.data.alarm;
        if (!this.alarm.transactions || this.alarm.transactions.length == 0) {
            this.alarmDataProvider.getSingleAlarm(this.alarm.fusionAlarmKey).subscribe((alarm: ControlAlarm) => {
                if (alarm) this.alarm = alarm;
            })
        }

        this.sub = this.signalr.alarmDataReceivedEvent$.subscribe((alarm: ControlAlarm) => {
            if (this.alarm) {
                if (alarm.controlAlarmId == this.alarm.controlAlarmId) {
                    this.alarm = alarm;
                    this.ackPressed = false;
                    this.resolvePressed = false;
                    this.bumpPressed = false;
                }
            }
        });
        if (this.alarm) {
            var temp = this.alarmDataProvider.getAlarm(this.alarm.fusionAlarmKey);
            if (temp) this.alarm = temp;
        }


        this.events.subscribe('AlarmSwitch', alarm => {
            if (this.alarm.fusionAlarmKey != alarm.fusionAlarmKey) {
                this.alarm = alarm;
                this.alarmDataProvider.getSingleAlarm(this.alarm.fusionAlarmKey).subscribe((alarm: ControlAlarm) => {
                    if (alarm) this.alarm = alarm;
                    this.liveValues.requestLiveValuesList([this.alarm.entityHardwareId]).subscribe();
                    this.ackPressed = false;
                    this.resolvePressed = false;
                    this.bumpPressed = false;
                })
            }
        })
    }

    ionViewWillUnload() {
        console.log("Unloaded from Actions")
        if(this.sub) this.sub.unsubscribe();    }

    public handleRefresh(event) {
        this.alarmDataProvider.getSingleAlarm(this.alarm.fusionAlarmKey).subscribe((alarm: ControlAlarm) => {
            if (alarm) this.alarm = alarm;
            window.setTimeout(() => event.complete(), 500);
        }, err => {
            window.setTimeout(() => event.complete(), 500);
        })
    }

    public alarmColor() {
        if (!this.alarm) return null;
        if (this.alarm.state != AlarmState.Resolved && this.alarm.silent) return 'primary';
        switch (this.alarm.state) {
            case AlarmState.Active:
                return 'alarm0';
            case AlarmState.Acknowledged:
                return 'alarm1';
            case AlarmState.Resolved:
                return 'alarm2';
        }
    }

    popBack() {
        if (this.nav) this.nav.pop();
        else this.events.publish("PopToRoot");
    }

    goToSettings() {
        var entity = this.roomData.getEntity(this.alarm.hardwareId);
        if (entity) {
            var type = null;
            if (entity.sensorSerialNumber) type = EntityType.Sensor;
            else if (entity.deviceSerialNumber) type = EntityType.Device;

            var sensorData: any = {
                entityNumber: this.alarm.hardwareId,
                roomId: this.alarm.roomProgramId,
                controlSerialNumber: entity.controlSerialNumber, controlName: this.alarm.controlName,
                deviceName: entity.sensorName || entity.deviceName,
                remoteSettingType: RemoteSettingCommandType.AlarmSettings,
                entityType: type
            }
            this.navCtrl.push('remote-settings-page', sensorData);
        }
        else {
            var roomData: any = {
                entityNumber: this.alarm.hardwareId, 
                roomId: this.alarm.hardwareId,
                controlSerialNumber: this.alarm.controlSerialNumber,
                controlName: this.alarm.controlName, 
                deviceName: "",
                remoteSettingType: RemoteSettingCommandType.AlarmSettings,
                entityType: EntityType.Room
            }
            this.navCtrl.push('remote-settings-page', roomData);
        }
    }

    getNextBumpTime() {
        if (!this.alarm.alarmGroupId || this.alarm.state != AlarmState.Active || this.alarm.transactions.length == 0) return null;
        if (this.alarm.bumpTime < 2) this.alarm.bumpTime = 2;
        return moment(this.alarm.transactions[0].created).add(this.alarm.bumpTime, 'minutes').toDate();
    }

    getAckExpireTime() {
        if (this.alarm.state != AlarmState.Acknowledged || this.alarm.transactions.length == 0) return null;
        if (this.alarm.ackTime < 2) this.alarm.ackTime = 2;
        return moment(this.alarm.transactions[0].created).add(this.alarm.ackTime, 'minutes').toDate();
    }

    isAtBumpLimit(){
        return moment().diff(moment(this.alarm.created), "minutes") > 2880;
    }

    public acknowledgeAlarm() {
        this.ackPressed = true;
        this.bumpPressed = true;
        this.alarmDataProvider.acknowledgeAlarm(this.alarm).subscribe();
    }

    public resolveAlarm() {
        this.resolvePressed = true;
        this.ackPressed = true;
        this.bumpPressed = true;
        this.alarmDataProvider.resolveAlarm(this.alarm).subscribe();
    }

    public bumpAlarm() {
        this.bumpPressed = true;
        this.alarmDataProvider.bumpAlarm(this.alarm).subscribe();
    }

    public canAcknowledge() {
        if (this.ackPressed || this.resolvePressed) return false;
        if (!this.alarm) return false;
        if (this.alarm.transactions.length == 0) return false;
        if (this.alarm.state == AlarmState.Active) {
            if (!this.alarm.alarmGroup) return true;
            if (this.alarm.alarmGroup.members.find(agm => agm.userId == localStorage.getItem('user_id'))) return true;
        }
        if (this.alarm.state == AlarmState.Acknowledged) {
            if (this.alarm.transactions[0].assignedToId == localStorage.getItem('user_id')) return true;
        }
        return false;
    }

    public canResolve() {
        if (this.resolvePressed) return false;
        if (!this.alarm) return false;
        if (this.alarm.transactions.length == 0) return false;
        if (this.alarm.state != AlarmState.Resolved) {
            if (!this.alarm.alarmGroup) return true;
            if (this.alarm.alarmGroup.members.find(agm => agm.userId == localStorage.getItem('user_id'))) return true;
        }
        return false;
    }

    public canBump() {
        if (this.bumpPressed || this.resolvePressed) return false;
        if (!this.alarm) return false;
        if (this.alarm.transactions.length == 0) return false;
        if (!this.alarm.alarmGroup) return false;
        if (this.alarm.state == AlarmState.Active) {
            if (this.alarm.assignedToId == localStorage.getItem(DBKeys.USER_ID)) return true;
        }

        return false;
    }

    toggleCard(card: string){
        console.log("Toggle card")
        switch(card){
            case 'actions':
                if(this.actionsExpanded) this.actionsExpanded = false;
                else this.actionsExpanded = true;
                console.log('Toggle card returning')
                return;
            case 'members':
                if(this.membersExpanded) this.membersExpanded = false;
                else this.membersExpanded = true;
                return;
            case 'assignments':
                if(this.assignmentsExpanded) this.assignmentsExpanded = false;
                else this.assignmentsExpanded = true;
                return;
        }
    }

    goToRemoteControl(){
        var device = this.roomData.getEntity(this.alarm.hardwareId);
        this.navCtrl.push('remote-control-page', device);
    }
}