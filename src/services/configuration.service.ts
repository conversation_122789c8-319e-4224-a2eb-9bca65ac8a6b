import { Injectable } from "@angular/core";

export class ConfigurationService {
    // public static baseUrl: string = 'http://localhost:5000';
    // public static baseUrl: string = 'http://*************:5000';
    public static baseUrl: string = 'https://controltechonline.azurewebsites.net';
    public static fusionLightWebUrl: string = 'https://lite.flightline-control.com';
    public static deviceTokenUrl: string;

    public static oneSignalAppId: string = '************************************'; // production
    // public static oneSignalAppId: string = '************************************'; // test
    public static adminId:string = '04b587d3-28d2-4ee6-ac31-c5d8f68acc20';
    
    //For Remote settings use
    public static constConditionDependentFalse: string = "dependent_false";
    public static constConditionDependentTrue: string = "dependent_true";
    public static constEnabledIfequal: string = "dependent_value_equal";
    public static constEnabledIfNotEqual: string = "dependent_value_not_equal";
    public static constEnabledIfGreaterThan: string = "dependent_value_greater_than";
    public static constEnabledIfLessThan: string = "dependent_value_less_than";
    public static constEnabledIfDifferent: string = "dependent_value_different";
    public static manualFireButtonName: string = "Manual fire";

    constructor() {
        ConfigurationService.deviceTokenUrl = ConfigurationService.baseUrl + '/api/DeviceToken';
    }
}