import { Injectable, Injector } from "@angular/core";
import { HttpClient, HttpHandler, HttpEvent, HttpRequest, HttpErrorResponse } from "@angular/common/http";
import { ObservableInput } from "rxjs/Observable";
import { Observable } from "rxjs/Observable";
import { ErrorObservable } from "rxjs/observable/ErrorObservable";
import { catchError, retry, debounce } from 'rxjs/operators';



@Injectable()
export class HttpService {

    constructor(private httpClient: HttpClient) {

    }

    get<T>(url: string, options?) {
        return this.httpClient.get<T>(url, options)
            .pipe(
                retry(2),
                catchError(this.handleError));

    }

    post<T>(url: string, body: any | undefined, options?) {
        return this.httpClient.post<T>(url, body, options)
            .pipe(
                retry(2),
                catchError(this.handleError));

    }

    put<T>(url: string, body: any | undefined, options?) {
        return this.httpClient.put<T>(url, body, options)
            .pipe(
                retry(2),
                catchError(this.handleError));
    }

    delete<T>(url: string, options?) {
        return this.httpClient.delete<T>(url, options)
            .pipe(
                retry(2),
                catchError(this.handleError));
    }

    private handleError(error: HttpErrorResponse) {
        if (error.error instanceof ErrorEvent) {
            // a Client-side or network error occured. Handle it accordingly.
            console.error('An error occured:', error.error.message);
        } else {
            // The backend returned an unsuccessful response code.
            // The response body may contain clues as to what went wrong,
            console.error(`Backend returned code ${error.status}, body was: ${error.error}`);
        }

        return new ErrorObservable('There was an error processing the request.');

    }

}